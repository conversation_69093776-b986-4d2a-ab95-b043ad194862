package org.springblade.common.mq.kafka.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

public class KafkaAssignParallelPool {
	ThreadPoolExecutor runningTaskThreadPool;
	List<KafkaAssignParallelTask> tasks = new ArrayList();
	private static Logger logger = LoggerFactory.getLogger(KafkaAssignParallelPool.class);
	private JSONObject kafkaProperties;
	Map<String, KafkaDataHandler> dataHandlerMap;

	public KafkaAssignParallelPool(int allThreadCnt, List<String> topics, Map<String, KafkaDataHandler> dataHandlerMap, JSONObject jsonProperties) {
		if (null == this.kafkaProperties) {
			this.kafkaProperties = new JSONObject();
			this.kafkaProperties.put("isoc.kafka.bootstrapServer", jsonProperties.getString("bootstrapServer"));
			this.kafkaProperties.put("isoc.kafka.consumer.group.id", "skyWorthGroupIdForEnergyDevice");
			if (null != jsonProperties && !jsonProperties.isEmpty()) {
				this.kafkaProperties.put("properties", jsonProperties);
				jsonProperties.remove("bootstrapServer");
			}
		}

		this.runningTaskThreadPool = new ThreadPoolExecutor(allThreadCnt, allThreadCnt, 5L, TimeUnit.SECONDS, new ArrayBlockingQueue(allThreadCnt), Executors.defaultThreadFactory(), new ThreadPoolExecutor.DiscardOldestPolicy());
		this.kafkaProperties.put("topics", topics);
		this.kafkaProperties.put("allThreadCnt", allThreadCnt);
		this.dataHandlerMap = dataHandlerMap;

		for(int i = 0; i < allThreadCnt; ++i) {
			JSONObject taskProperties = new JSONObject();
			taskProperties.putAll(this.kafkaProperties);
			taskProperties.put("currentNumber", i);
			KafkaAssignParallelTask kafkaParallelTask = new KafkaAssignParallelTask(taskProperties, dataHandlerMap);
			this.tasks.add(kafkaParallelTask);
		}

	}

	public void startHandler() {
		Iterator var1 = this.tasks.iterator();

		while(var1.hasNext()) {
			KafkaAssignParallelTask task = (KafkaAssignParallelTask)var1.next();
			this.runTask(task);
		}

	}

	private void runTask(KafkaAssignParallelTask task) {
		CompletableFuture.runAsync(task, this.runningTaskThreadPool).exceptionally((e) -> {
			String message = e.getMessage();
			String messageJson = message.split("HandlerDataException:")[1];
			JSONObject jsonObject = JSON.parseObject(messageJson);
			String currentNumber = jsonObject.getString("currentNumber");
			logger.info("currentNumber=" + currentNumber);
			KafkaAssignParallelTask oldTask = (KafkaAssignParallelTask)this.tasks.get(Integer.parseInt(currentNumber));
			oldTask.stop();
			JSONObject taskProperties = new JSONObject();
			taskProperties.putAll(this.kafkaProperties);
			taskProperties.put("currentNumber", currentNumber);
			KafkaAssignParallelTask kafkaParallelTask = new KafkaAssignParallelTask(taskProperties, this.dataHandlerMap);
			this.tasks.set(Integer.parseInt(currentNumber), kafkaParallelTask);
			logger.info("任务异常 10 秒后 重启");

			try {
				Thread.sleep(10000L);
			} catch (InterruptedException var10) {
				Thread.currentThread().interrupt();
				logger.error(e.getMessage(), e);
			}

			this.runTask(kafkaParallelTask);
			return null;
		});
	}

	public void stop() {
		Iterator var1 = this.tasks.iterator();

		while(var1.hasNext()) {
			KafkaAssignParallelTask task = (KafkaAssignParallelTask)var1.next();
			task.stop();
		}

		this.runningTaskThreadPool.shutdown();
	}

	public static void main(String[] args) {
	}
}
