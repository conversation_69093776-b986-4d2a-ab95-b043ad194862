package org.springblade.common.mq.kafka.config;

import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.errors.WakeupException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class KafkaAssignParallelTask implements Runnable{
	private static Logger logger = LoggerFactory.getLogger(KafkaAssignParallelTask.class);
	private ParallelDataSource dataSource;
	private String currentNumber;
	Map<String, KafkaDataHandler> dataHandlerMap;
	Map<String, AtomicInteger> errorNumMap = new ConcurrentHashMap();

	public KafkaAssignParallelTask(JSONObject kafkaProperties, Map<String, KafkaDataHandler> dataHandlerMap) {
		this.dataSource = new KafkaAssignParalleDataSource(kafkaProperties);
		this.dataSource.initDataSource();
		this.currentNumber = kafkaProperties.getString("currentNumber");
		this.dataHandlerMap = dataHandlerMap;
		this.errorNumMap.put(this.currentNumber, new AtomicInteger(1));
	}

	public void run() {
		while(true) {
			List<JSONObject> data = null;

			try {
				data = this.dataSource.getData();
			} catch (WakeupException var10) {
				return;
			} catch (Exception var11) {
				String message = "拉取数据异常:" + Thread.currentThread().getName();
				logger.error(var11.getMessage(), var11);
				var11.printStackTrace();
				JSONObject json = new JSONObject();
				json.put("currentNumber", this.currentNumber);
				json.put("message", message);
				int errNum = ((AtomicInteger)this.errorNumMap.get(this.currentNumber)).incrementAndGet();
				json.put("errorNum", errNum);
				if (errNum < 10) {
					logger.info("KafkaAssignParallelTask getData error num: {}",errNum);
					throw new HandlerDataException(json.toJSONString(), var11);
				}
			}

			Map<TopicPartition, OffsetAndMetadata> map = new HashMap();
			Map<String, List<JSONObject>> mapData = new HashMap();
			if (null != data) {
				data.forEach((jsonx) -> {
					String topic = jsonx.getString("topic");
					String offset = jsonx.getString("offset");
					String partition = jsonx.getString("partition");
					TopicPartition topicPartition = new TopicPartition(topic, Integer.parseInt(partition));
					OffsetAndMetadata offsetAndMetadata = new OffsetAndMetadata(Long.parseLong(offset) + 1L);
					map.put(topicPartition, offsetAndMetadata);
					List<JSONObject> jsonObjects = (List)mapData.get(topic);
					if (jsonObjects == null) {
						List<JSONObject> list = new ArrayList();
						list.add(jsonx);
						mapData.put(topic, list);
					} else {
						jsonObjects.add(jsonx);
					}

				});

				try {
					mapData.entrySet().forEach((entry) -> {
						String topic = (String)entry.getKey();
						List<JSONObject> list = (List)entry.getValue();
						((KafkaDataHandler)this.dataHandlerMap.get(topic)).handlerData(list);
					});
					this.dataSource.commit(map);
				} catch (Exception var9) {
					String message = "处理数据异常:" + Thread.currentThread().getName();
					logger.info(message);
					var9.printStackTrace();
					JSONObject json = new JSONObject();
					json.put("currentNumber", this.currentNumber);
					json.put("message", message);
					int errNum = ((AtomicInteger)this.errorNumMap.get(this.currentNumber)).incrementAndGet();
					json.put("errorNum", errNum);
					if (errNum < 10) {
						logger.info("KafkaAssignParallelTask commit error num: {}",errNum);
						throw new HandlerDataException(json.toJSONString(), var9);
					}else {
						logger.info("KafkaAssignParallelTask commit error more than 10 num: {}",errNum);
						// 重试10次失败后，如果再失败，需要提交此次消息的偏移量，否则一直重复消费这个消息
						this.dataSource.commit(map);
					}
				}
			}

			try {
				Thread.sleep(200L);
			} catch (InterruptedException var8) {
				Thread.currentThread().interrupt();
				logger.error(var8.getMessage(), var8);
			}
		}
	}

	public void stop() {
		this.dataSource.close();
	}
}
