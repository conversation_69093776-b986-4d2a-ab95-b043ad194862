server:
  port: 8090

spring:
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true #开启从注册中心动态创建路由的功能，利用微服务名进行路由
      routes:
        - id: skyworth-client
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://skyworth-client
          predicates:
            - Path=/skyworth-client/**
        - id: skyworth-toolkit
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://skyworth-toolkit
          predicates:
            - Path=/skyworth-toolkit/**
        - id: blade-system
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://blade-system
          predicates:
            - Path=/blade-system/**
        - id: xxl-job-admin
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://xxl-job-admin
          predicates:
            - Path=/xxl-job-admin/**
        - id: blade-resource
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://blade-resource
          predicates:
            - Path=/blade-resource/**
        - id: blade-auth
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://blade-auth
          predicates:
            - Path=/blade-auth/**
        - id: blade-log
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://blade-log
          predicates:
            - Path=/blade-log/**
        - id: blade-flow
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://blade-flow
          predicates:
            - Path=/blade-flow/**
        - id: skyworth-agent
          # 采用 LoadBalanceClient 方式请求，以 lb:// 开头，后面的是注册在 Nacos 上的服务名
          uri: lb://skyworth-agent
          predicates:
            - Path=/skyworth-agent/**
    loadbalancer:
      retry:
        enabled: true
logging:
  config: classpath:gateway-logback.xml
