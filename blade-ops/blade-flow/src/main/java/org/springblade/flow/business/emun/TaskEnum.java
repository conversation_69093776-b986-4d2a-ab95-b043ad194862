package org.springblade.flow.business.emun;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum TaskEnum {


	/**
	 * 指派踏勘任务
	 */
	Survey_WO_Assignment(1, "surveyWoAssign"),

	/**
	 * 踏勘及业主确认
	 */
	SURVEY_LL_VERIFICATION(2, "surveyLlVerification"),

	/**
	 * 指派施工任务
	 */
	Installation_WO_Assignment(3, "InstallWoAssignment"),

	/**
	 * 物料签收
	 */
	MATERIAL_COLLECTION_SIGN(4, "materialCollection"),

	/**
	 * 施工安全信息提交
	 */
	EHS_SUBMISSION(5, "ehsSubmission"),

	/**
	 * 施工安全信息审核 EHS Verification
	 */
	EHS_Verification(6, "ehsVerification"),

	/**
	 * 质检信息提交
	 */
	QC_SUBMISSION(7, "qcSubmission"),

	/**
	 * 质检信息审核 QC Verification
	 */
	Q_Verification(8, "qcVerification"),

	/**
	 * 临时COC上传
	 */
	TEMPORARY_COC(9, "temporaryCoc"),

	/**
	 * 业主验收
	 */
	FAC(10, "fac"),

	/**
	 * 正式COC上传
	 */
	OFFICIAL_COC(11, "officialCoc");

	/**
	 * 类型
	 */
	private final int type;

	/**
	 * 描述
	 */
	private final String description;


	public static TaskEnum of(String taskName) {
		if (taskName == null) {
			return null;
		}
		TaskEnum[] values = TaskEnum.values();
		for (TaskEnum taskEnum : values) {
			if (Objects.equals(taskEnum.description, taskName)) {
				return taskEnum;
			}
		}
		return null;
	}

}
