/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service.impl;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.metadata.IPage;
import liquibase.repackaged.org.apache.commons.text.StringEscapeUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.persistence.entity.CommentEntityImpl;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.constant.OrderStatusConstants;
import org.skyworth.ess.dto.OrderWorkFlowDTO;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.business.constant.FlowConstant;
import org.springblade.flow.business.emun.TaskEnum;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.dto.PurviewDTO;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.core.vo.*;
import org.springblade.flow.engine.constant.FlowEngineConstant;
import org.springblade.flow.engine.entity.FlowProcess;
import org.springblade.flow.engine.utils.FlowCache;
import org.springblade.flow.engine.utils.FlowRole;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Role;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程业务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class FlowBusinessServiceImpl implements FlowBusinessService {

	private final TaskService taskService;
	private final HistoryService historyService;
	private final IUserClient iUserClient;
	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final ISysClient iSysClient;
	private final IAgentClient iAgentClient;
	private final IDictBizClient bizClient;
	private final IUserSearchClient iUserSearchClient;
	private static final String USR_TASK = "userTask";

	@Override
	public IPage<BladeFlow> selectClaimPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		String taskGroup = TaskUtil.getCandidateGroup();
		List<BladeFlow> flowList = new LinkedList<>();

		// 个人等待签收的任务
		TaskQuery claimUserQuery = taskService.createTaskQuery().taskCandidateUser(taskUser)
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		// 定制流程等待签收的任务
		TaskQuery claimRoleWithTenantIdQuery = taskService.createTaskQuery().taskTenantId(AuthUtil.getTenantId()).taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		// 通用流程等待签收的任务
		TaskQuery claimRoleWithoutTenantIdQuery = taskService.createTaskQuery().taskWithoutTenantId().taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();

		// 构建列表数据
		buildFlowTaskList(bladeFlow, flowList, claimUserQuery, FlowEngineConstant.STATUS_CLAIM);
		buildFlowTaskList(bladeFlow, flowList, claimRoleWithTenantIdQuery, FlowEngineConstant.STATUS_CLAIM);
		buildFlowTaskList(bladeFlow, flowList, claimRoleWithoutTenantIdQuery, FlowEngineConstant.STATUS_CLAIM);

		// 计算总数
		long count = claimUserQuery.count() + claimRoleWithTenantIdQuery.count() + claimRoleWithoutTenantIdQuery.count();
		// 设置页数
		page.setSize(count);
		// 设置总数
		page.setTotal(count);
		// 设置数据
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectTodoPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		// 已签收的任务
		TaskQuery todoQuery = taskService.createTaskQuery().taskAssignee(taskUser).active()
			.includeProcessVariables().orderByTaskCreateTime().desc();

		// 构建列表数据
		buildFlowTaskList(bladeFlow, flowList, todoQuery, FlowEngineConstant.STATUS_TODO);

		// 计算总数
		long count = todoQuery.count();
		// 设置页数
		page.setSize(count);
		// 设置总数
		page.setTotal(count);
		// 设置数据
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectSendPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		HistoricProcessInstanceQuery historyQuery = historyService.createHistoricProcessInstanceQuery().startedBy(taskUser).orderByProcessInstanceStartTime().desc();

		if (bladeFlow.getCategory() != null) {
			historyQuery.processDefinitionCategory(bladeFlow.getCategory());
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			historyQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			historyQuery.startedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			historyQuery.startedBefore(bladeFlow.getEndDate());
		}

		// 查询列表
		List<HistoricProcessInstance> historyList = historyQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));

		historyList.forEach(historicProcessInstance -> {
			BladeFlow flow = new BladeFlow();
			// historicProcessInstance
			flow.setCreateTime(historicProcessInstance.getStartTime());
			flow.setEndTime(historicProcessInstance.getEndTime());
			flow.setVariables(historicProcessInstance.getProcessVariables());
			String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
			if (businessKey.length > 1) {
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
			}
			flow.setHistoryActivityName(historicProcessInstance.getName());
			flow.setProcessInstanceId(historicProcessInstance.getId());
			flow.setHistoryProcessInstanceId(historicProcessInstance.getId());
			// ProcessDefinition
			FlowProcess processDefinition = FlowCache.getProcessDefinition(historicProcessInstance.getProcessDefinitionId());
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flow.setProcessInstanceId(historicProcessInstance.getId());
			// HistoricTaskInstance
			List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
			if (Func.isNotEmpty(historyTasks)) {
				HistoricTaskInstance historyTask = historyTasks.iterator().next();
				flow.setTaskId(historyTask.getId());
				flow.setTaskName(historyTask.getName());
				flow.setTaskDefinitionKey(historyTask.getTaskDefinitionKey());
			}
			// Status
			if (historicProcessInstance.getEndActivityId() != null) {
				flow.setProcessIsFinished(FlowEngineConstant.STATUS_FINISHED);
			} else {
				flow.setProcessIsFinished(FlowEngineConstant.STATUS_UNFINISHED);
			}
			flow.setStatus(FlowEngineConstant.STATUS_FINISH);
			flowList.add(flow);
		});

		// 计算总数
		long count = historyQuery.count();
		// 设置总数
		page.setTotal(count);
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectDonePage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		HistoricTaskInstanceQuery doneQuery = historyService.createHistoricTaskInstanceQuery().taskAssignee(taskUser).finished()
			.includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc();

		if (bladeFlow.getCategory() != null) {
			doneQuery.processCategoryIn(Func.toStrList(bladeFlow.getCategory()));
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			doneQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			doneQuery.taskCompletedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			doneQuery.taskCompletedBefore(bladeFlow.getEndDate());
		}

		// 查询列表
		List<HistoricTaskInstance> doneList = doneQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));
		doneList.forEach(historicTaskInstance -> {
			BladeFlow flow = new BladeFlow();
			flow.setTaskId(historicTaskInstance.getId());
			flow.setTaskDefinitionKey(historicTaskInstance.getTaskDefinitionKey());
			flow.setTaskName(historicTaskInstance.getName());
			flow.setAssignee(historicTaskInstance.getAssignee());
			flow.setCreateTime(historicTaskInstance.getCreateTime());
			flow.setExecutionId(historicTaskInstance.getExecutionId());
			flow.setHistoryTaskEndTime(historicTaskInstance.getEndTime());
			flow.setVariables(historicTaskInstance.getProcessVariables());

			FlowProcess processDefinition = FlowCache.getProcessDefinition(historicTaskInstance.getProcessDefinitionId());
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));

			flow.setProcessInstanceId(historicTaskInstance.getProcessInstanceId());
			flow.setHistoryProcessInstanceId(historicTaskInstance.getProcessInstanceId());
			HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance((historicTaskInstance.getProcessInstanceId()));
			if (Func.isNotEmpty(historicProcessInstance)) {
				String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
				if (historicProcessInstance.getEndActivityId() != null) {
					flow.setProcessIsFinished(FlowEngineConstant.STATUS_FINISHED);
				} else {
					flow.setProcessIsFinished(FlowEngineConstant.STATUS_UNFINISHED);
				}
			}
			flow.setStatus(FlowEngineConstant.STATUS_FINISH);
			flowList.add(flow);
		});
		// 计算总数
		long count = doneQuery.count();
		// 设置总数
		page.setTotal(count);
		page.setRecords(flowList);
		return page;
	}

	@Override
	public boolean completeTask(BladeFlow flow) {
		String taskId = flow.getTaskId();
		String processInstanceId = flow.getProcessInstanceId();
		String comment = Func.toStr(flow.getComment(), ProcessConstant.PASS_COMMENT);
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, comment);
		}
		// 创建变量
		Map<String, Object> variables = flow.getVariables();
		if (variables == null) {
			variables = Kv.create();
		}
		variables.put(ProcessConstant.PASS_KEY, flow.isPass());
		// 完成任务
		taskService.complete(taskId, variables);
		return true;
	}


	/**
	 * 构建流程
	 *
	 * @param bladeFlow 流程通用类
	 * @param flowList  流程列表
	 * @param taskQuery 任务查询类
	 * @param status    状态
	 */
	private void buildFlowTaskList(BladeFlow bladeFlow, List<BladeFlow> flowList, TaskQuery taskQuery, String status) {
		if (bladeFlow.getCategory() != null) {
			taskQuery.processCategoryIn(Func.toStrList(bladeFlow.getCategory()));
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			taskQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			taskQuery.taskCreatedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			taskQuery.taskCreatedBefore(bladeFlow.getEndDate());
		}
		taskQuery.list().forEach(task -> {
			BladeFlow flow = new BladeFlow();
			flow.setTaskId(task.getId());
			flow.setTaskDefinitionKey(task.getTaskDefinitionKey());
			flow.setTaskName(task.getName());
			flow.setAssignee(task.getAssignee());
			flow.setCreateTime(task.getCreateTime());
			flow.setClaimTime(task.getClaimTime());
			flow.setExecutionId(task.getExecutionId());
			flow.setVariables(task.getProcessVariables());

			HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(task.getProcessInstanceId());
			if (Func.isNotEmpty(historicProcessInstance)) {
				String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
			}

			FlowProcess processDefinition = FlowCache.getProcessDefinition(task.getProcessDefinitionId());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setProcessInstanceId(task.getProcessInstanceId());
			flow.setStatus(status);
			flowList.add(flow);
		});
	}

	/**
	 * 获取历史流程
	 *
	 * @param processInstanceId 流程实例id
	 * @return HistoricProcessInstance
	 */
	private HistoricProcessInstance getHistoricProcessInstance(String processInstanceId) {
		return historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
	}

	/**
	 * 审批历史记录
	 *
	 * @param processInstanceId 流程实例ID
	 * @return List<OrderFlowVO>
	 */
	@Override
	public List<HistoryVO> getTaskHistory(String processInstanceId) {
		List<HistoryVO> historyList = new ArrayList<>();
		List<BladeFlow> bladeFlows = getHistory(processInstanceId);
		bladeFlows.forEach(flow -> {
			HistoryVO historyVO = new HistoryVO();
			BeanUtils.copyProperties(flow, historyVO);
			if (historyVO.getPass()) {
				historyVO.setColor(FlowConstant.GREEN);
				//最终节点的时间+1s用于排序
				if (StringUtil.isBlank(historyVO.getAssigneeName())) {
					historyVO.setCreateTime(new Date(historyVO.getCreateTime().getTime() + 1000));
				}
			} else if (StringUtil.isBlank(historyVO.getComment())) {
				historyVO.setColor(FlowConstant.YELLOW);
				//进行节点的时间+1s用于排序
				historyVO.setCreateTime(new Date(historyVO.getCreateTime().getTime() + 1000));
			} else {
				//流程节点为 Payment Confirmation Balance Payment
				if (FlowConstant.LL_INFO_CONFIRMATION.equals(historyVO.getTaskName()) || FlowConstant.PAYMENT_CONFIRMATION.equals(historyVO.getTaskName())) {
					historyVO.setColor(FlowConstant.BLUE);
				} else {
					historyVO.setColor(FlowConstant.RED);
				}
			}
			historyList.add(historyVO);
		});
		List<HistoryVO> historyNew = historyList.stream().sorted(Comparator.comparing(HistoryVO::getCreateTime))
			.collect(Collectors.toList());
		//流程图中包含取消流程 蓝色 则最后一个节点无色
		boolean b = historyNew.stream().anyMatch(m -> m.getColor().equals(FlowConstant.BLUE));
		if (b) {
			historyNew.remove(historyList.size() - 1);
		}
		return historyNew;

	}

	private List<BladeFlow> getHistory(String processInstanceId) {
		List<BladeFlow> bladeFlows = new ArrayList<>();
		//获取历史任务
		List<HistoricActivityInstance> listHistory = historyService.createHistoricActivityInstanceQuery()
			.processInstanceId(processInstanceId)
			.orderByActivityId()
			.desc().list();
		listHistory.forEach(hi -> {
			if (StringUtils.equals(USR_TASK, hi.getActivityType())
				|| FlowEngineConstant.START_EVENT.equals(hi.getActivityType())
				|| FlowEngineConstant.END_EVENT.equals(hi.getActivityType())) {
				BladeFlow flow = new BladeFlow();
				flow.setTaskName(hi.getActivityName());
				flow.setTaskId(hi.getTaskId());
				if (hi.getEndTime() == null) {
					flow.setCreateTime(hi.getStartTime());
				} else {
					flow.setCreateTime(hi.getEndTime());
				}
				//开始节点、结束节点默认通过
				if (FlowEngineConstant.START_EVENT.equals(hi.getActivityType()) || FlowEngineConstant.END_EVENT.equals(hi.getActivityType())) {
					flow.setFlag("ok");
				}
				//流程启动人
				if (FlowEngineConstant.START_EVENT.equals(hi.getActivityType())) {
					List<HistoricProcessInstance> processInstanceList = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).orderByProcessInstanceStartTime().asc().list();
					if (!processInstanceList.isEmpty()) {
						if (StringUtil.isNotBlank(processInstanceList.get(0).getStartUserId())) {
							String taskUser = processInstanceList.get(0).getStartUserId();
							flow.setAssignee(String.valueOf(TaskUtil.getUserId(taskUser)));
						}
					}
				} else {
					flow.setAssignee(String.valueOf(TaskUtil.getUserId(hi.getAssignee())));
				}

				bladeFlows.add(flow);
			}
		});
		//获取历史意见
		List<Comment> comments = taskService.getProcessInstanceComments(processInstanceId);
		bladeFlows.forEach(bla -> comments.stream()
			.filter(comment -> "comment".equals(comment.getType())).filter(m -> Objects.equals(m.getTaskId(), bla.getTaskId()))
			.findFirst().map(m -> {
				bla.setAssignee(String.valueOf(TaskUtil.getUserId(m.getUserId())));
				bla.setComment(m.getFullMessage());
				return bla;
			}));
		//获取流程流转用户ID集合
		List<Long> userIds = bladeFlows.stream().map(BladeFlow::getAssignee).map(Long::valueOf).distinct().collect(Collectors.toList());
		//获取用户信息
		Map<Long, String> userMap = iUserSearchClient.listAllByUserIds(userIds).getData().stream().collect(Collectors.toMap(User::getId, User::getRealName));
		bladeFlows.forEach(bladeFlow -> {
			//取到用户对应的realName
			bladeFlow.setAssigneeName(userMap.get(Func.toLong(bladeFlow.getAssignee())));
		});
		//获取审批标识
		bladeFlows.forEach(bla -> comments.stream().filter(comment -> "flag".equals(comment.getType()))
			.filter(m -> Objects.equals(m.getTaskId(), bla.getTaskId()))
			.findFirst().map(m -> {
				bla.setFlag(((CommentEntityImpl) m).getMessage());
				return bla;
			}));
		return bladeFlows;
	}

	@Override
	public OrderFlowVO startOrderProcess(String businessId) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		Map<String, String> map = getFlowKey();
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(FlowEngineConstant.PROCESS_DEFINITION_KEY, businessId);
		//获取流程活动任务
		Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).active().singleResult();
		// 组装流程通用类
		OrderFlowVO flow = new OrderFlowVO();
		String taskName = map.get(task.getName());
		flow.setProcessInstanceId(processInstance.getId());
		flow.setCreateTime(processInstance.getStartTime());
		flow.setTaskName(taskName);
		flow.setTaskId(task.getId());
		flow.setAssignee(FlowRole.getRole().get(taskName));
		flow.setAssigneeName(getRoleName(FlowRole.getRole().get(taskName)));
		return flow;
	}

	/**
	 * 多个角色获取多个ID
	 */
	@Nullable
	private String getRoleName(String roleCode) {
		if (StringUtil.isNoneBlank(roleCode)) {
			if (roleCode.contains(StrPool.COMMA)) {
				StringBuilder str = new StringBuilder();
				String[] result = roleCode.split(StrPool.COMMA);
				for (int i = 0; i < result.length; i++) {
					if (i != 0) {
						str.append(StrPool.COMMA);
					}
					str.append(iSysClient.getRoleInfoByCode(result[i]).getData().getRoleName());

				}
				return str.toString();
			}
			return iSysClient.getRoleInfoByCode(roleCode).getData().getRoleName();
		}
		return null;
	}

	@Override
	public R<ApprovalVO> completeOrderTask(OrderFlowDTO orderFlowDTO) {
		// 增加评论
		String taskId = orderFlowDTO.getTaskId();
		String taskName = orderFlowDTO.getTaskName();
		String processInstanceId = orderFlowDTO.getProcessInstanceId();
		boolean flag=orderFlowDTO.getFlag();
		Map<String, Object> variables = orderFlowDTO.getVariables();
		// 非空判断
		if (Func.isEmpty(variables)) {
			variables = Kv.create();
		}
		Object siteEngineer = variables.get("siteEngineer");
		Object electrician = variables.get("electrician");
		Object comment = variables.get("comment");
		//指定人的节点传参校验
		if (check(taskName, flag, siteEngineer, electrician)) {
			return R.fail("siteEngineer、electrician cannot be empty.");
		}
		//查询一下当前节点ID 节点名称 是否和用户传的匹配
		boolean isEqual = false;
		Task taskBefore = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
		if (taskBefore != null) {
			if (StringUtil.isNoneBlank(taskBefore.getId())) {
				isEqual = taskBefore.getId().equals(taskId);
			}
			if (!isEqual) {
				return R.fail("Process mismatch ！taskId is: " + taskBefore.getId() + " taskName is: " + taskBefore.getName());
			}
		} else {
			return R.fail("Process has ended");
		}
		//驳回时审批意见必填
		if (!flag) {
			if (Func.isEmpty(comment)) {
				return R.fail("comment cannot be empty.");
			}
		}
		//流程节点操作人
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		//特殊字符转义处理
		String comments = StringEscapeUtils.unescapeHtml4((String) comment);
		//pass时审批 设置标识ok
		if (flag) {
			taskService.addComment(taskId, processInstanceId, "flag", "ok");
		}
		taskService.addComment(taskId, processInstanceId, comments);
		// 完成任务
		taskService.complete(taskId, variables);

		//获取流转后流程活动任务
		Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
		if (task != null) {
			return R.data(getOrderFlowVO(task, String.valueOf(siteEngineer), String.valueOf(electrician)));
		}
		return R.success("Process has ended");
	}

	private boolean check(String taskName,boolean flag, Object siteEngineer, Object electrician) {
		boolean isNull = false;
		TaskEnum taskEnum = TaskEnum.of(taskName);
		if (taskEnum != null) {
			switch (taskEnum.getType()) {
				//指派踏勘任务
				case 1:
					if(flag) {
						isNull = (Func.isEmpty(siteEngineer)
							|| Func.isEmpty(electrician));
					}
						break;
				//指派施工任务
				case 3:
					//物料签收
				case 4:
					//施工安全信息审核
				case 6:
					//施工审核上传临时COC
				case 9:
					isNull = Func.isEmpty(siteEngineer);
					break;
				//质检信息审核
				case 8:
					isNull = Func.isEmpty(electrician);
					break;
				default:
					break;
			}
		}
		return isNull;
	}
	@Override
	public HistoryVO getNewest(String processInstanceId) {
		List<HistoryVO> historyList = getTaskHistory(processInstanceId);
		if (CollectionUtil.isEmpty(historyList)) {
			return null;
		}
		return historyList.get(historyList.size() - 2);
	}

	@NotNull
	private ApprovalVO getOrderFlowVO(Task task, String siteEngineer, String electrician) {
		Map<String, String> map = getFlowKey();
		String taskName = map.get(task.getName());
		ApprovalVO flow = new ApprovalVO();
		flow.setCreateTime(task.getCreateTime());
		flow.setTaskName(taskName);
		flow.setTaskId(task.getId());
		TaskEnum taskEnum = TaskEnum.of(taskName);
		if (taskEnum != null) {
			switch (taskEnum.getType()) {
				//踏勘及业主确认
				case 2:
					flow.setAssignee(siteEngineer + "," + electrician);
					flow.setAssigneeName(iUserClient.userInfoById(Long.parseLong(siteEngineer)).getData().getRealName() + "," + iUserClient.userInfoById(Long.parseLong(electrician)).getData().getRealName());
					break;
				//物料签收
				case 4:
					//施工安全信息提交
				case 5:
					//质检信息提交
				case 7:
					//业主验收
				case 10:
					flow.setAssignee(siteEngineer);
					flow.setAssigneeName(iUserClient.userInfoById(Long.parseLong(siteEngineer)).getData().getRealName());
					break;
				//施工审核上传临时COC
				case 9:
					flow.setAssignee(electrician);
					flow.setAssigneeName(iUserClient.userInfoById(Long.parseLong(electrician)).getData().getRealName());
					break;
				//正式COC上传
				case 11:
					flow.setAssignee(electrician+ "," + FlowRole.getRole().get(taskName));
					flow.setAssigneeName(iUserClient.userInfoById(Long.parseLong(electrician)).getData().getRealName()+ "," +getRoleName(FlowRole.getRole().get(taskName)));
					break;
				default:
					flow.setAssignee(FlowRole.getRole().get(taskName));
					flow.setAssigneeName(getRoleName(FlowRole.getRole().get(taskName)));
					break;
			}
		} else {
			flow.setAssignee(FlowRole.getRole().get(taskName));
			flow.setAssigneeName(getRoleName(FlowRole.getRole().get(taskName)));
		}
		return flow;
	}


	@Override
	public List<ViewVO> getFlowView(String orderId) {
		//获取字典表中流程节点名称
		Map<String, String> map = getFlowKey();
		List<DictBiz> bizList = getFlowSort();
		List<ViewVO> voList = new ArrayList<>();
		bizList.forEach(biz -> {
			ViewVO vo = new ViewVO();
			vo.setTaskName(biz.getDictValue());
			vo.setTaskNameKey(biz.getDictKey());
			voList.add(vo);
		});
		//去掉数据库配置的cancel
		if (voList.size() == FlowConstant.TWENTY_TWO) {
			voList.remove(voList.size() - 1);
		}
		if (StringUtil.isNoneBlank(orderId)) {
			OrderWorkFlowDTO orderWorkFlow = new OrderWorkFlowDTO();
			orderWorkFlow.setOrderId(Long.valueOf(orderId));
			OrderWorkFlowVO orderWorkFlowVO = iAgentClient.getWorkFlowDataByOrderId(orderWorkFlow).getData();
			List<HistoryVO> listHistory = getTaskHistory(orderWorkFlowVO.getWfInstanceId());
			Map<String, List<HistoryVO>> groupListMap = listHistory.stream().collect(Collectors.groupingBy(HistoryVO::getTaskName));
			listHistory = groupListMap.values().stream().map(lists -> lists.get(lists.size() - 1))
				.sorted(Comparator.comparing(HistoryVO::getCreateTime))
				.collect(Collectors.toList());
			//获取审批日志
			List<ViewVO> vos = new ArrayList<>();
			listHistory.forEach(history -> {
				ViewVO vo = new ViewVO();
				BeanUtils.copyProperties(history, vo);
				vos.add(vo);
			});
			//更新流程图节点信息
			voList.forEach(bla -> vos.stream()
				.filter(m -> Objects.equals(bla.getTaskNameKey(), map.get(m.getTaskName())))
				.findFirst().map(m -> {
					bla.setColor(m.getColor());
					bla.setAssignee(m.getAssignee());
					bla.setPass(m.getPass());
					bla.setTaskId(m.getTaskId());
					bla.setAssigneeName(m.getAssigneeName());
					bla.setCreateTime(m.getCreateTime());
					return bla;
				}));
			//跨节点驳回 中间的节点也变为红色
			// 判断流程图中最后一个节点是否为驳回节点 如果是则驳回节点到当前节点都为红色
			int k = vos.size() - 1;
			if (StringUtil.isNoneBlank(voList.get(k).getColor())) {
				if (voList.get(k).getColor().equals(FlowConstant.RED)) {
					//获取黄色节点的下标
					int j = 0;
					for (int i = 0; i < voList.size(); i++) {
						if (FlowConstant.YELLOW.equals(voList.get(i).getColor()) || FlowConstant.BLUE.equals(voList.get(i).getColor())) {
							j = i;
							break;
						}
					}
					if (k - j > 1) {
						//截取到黄色节点到红色节点之间的数据【)
						voList.subList(j + 1, k)
							.forEach(viewVO -> viewVO.setColor(FlowConstant.RED));
					}
				}
			}
		}
		return voList;
	}

	@Override
	public PurviewVO getPurview(PurviewDTO purviewDTO) {
		//创维交付经理  016 系统管理员 拥有所有节点的编辑权限
		PurviewVO purviewVO = new PurviewVO();
		boolean flag;
		purviewVO.setFlag(false);
		String userId = String.valueOf(AuthUtil.getUserId());
		//useId为空时 框架默认-1
		if (FlowConstant.MINUS_ONE.equals(userId)) {
			return purviewVO;
		}
		//根据userId查询 用户包含的所有角色
		List<String> codes = iSysClient.findUserRoleInfoByUserId(userId).getData().stream()
			.map(Role::getRoleCode).collect(Collectors.toList());
		//用户角色不为空
		if (CollectionUtil.isNotEmpty(codes)) {
			//创建订单时 订单号为空 Rollout Manager Distributor/SD
			if (StringUtil.isBlank(purviewDTO.getOrderId())) {
				purviewVO.setFlag(check(codes, "007,012,016,018"));
				return purviewVO;
			}
			OrderWorkFlowDTO orderWorkFlow = new OrderWorkFlowDTO();
			orderWorkFlow.setOrderId(Long.valueOf(purviewDTO.getOrderId()));
			OrderWorkFlowVO orderWorkFlowVO = iAgentClient.getWorkFlowDataByOrderId(orderWorkFlow).getData();
			if (orderWorkFlowVO == null) {
				return purviewVO;
			}
			//通过订单号查询当前订单节点和处理人信息
			String roleType = orderWorkFlowVO.getWfCurrentType();
			String taskName = orderWorkFlowVO.getWfCurrentStatus();
			String aggeName = orderWorkFlowVO.getWfCurrentRole();
			if (StringUtil.isNoneBlank(taskName)) {
				//订单状态为关闭 办结 不能编辑
				if (taskName.equals(OrderStatusConstants.ORDER_STATUS_CLOSED) || taskName.equals(OrderStatusConstants.MAINTENANCE)) {
					return purviewVO;
				}
				if (StringUtil.isNoneBlank(roleType) && StringUtil.isNoneBlank(aggeName)) {
					if (taskName.equals(StringEscapeUtils.unescapeHtml4(purviewDTO.getTaskName()))) {
						//当前节点处理是人 匹配用户ID相同可以编辑
						//当前节点处理是角色 匹配用户角色相同可以编辑
						//超级管理员 创维交付经理 在当前节点都可以编辑
						flag = (check(codes, "007,016") || (roleType.equals(OrderStatusConstants.CURRENT_USER) && aggeName.contains(userId)) ||
							(roleType.equals(OrderStatusConstants.CURRENT_ROLE) && check(codes, aggeName))||(roleType.equals(OrderStatusConstants.CURRENT_USER_ROLE) && (check(codes, aggeName)||aggeName.contains(userId))));
						purviewVO.setFlag(flag);
						return purviewVO;
					}
				}
			}
		}
		return purviewVO;
	}


	/**
	 * 匹配用户角色相同
	 */
	private Boolean check(List<String> roleIds, String aggeName) {
		for (String roleId : roleIds) {
			if (aggeName.contains(roleId)) {
				return true;
			}
		}
		return false;
	}

	private Map<String, String> getFlowKey() {
		List<DictBiz> bizList = bizClient.getListByLang("wf_schedule", "en").getData();
		return bizList.stream().collect(HashMap::new, (stringStringHashMap, dictBiz) -> stringStringHashMap.put(dictBiz.getDictValue(), dictBiz.getDictKey()), HashMap::putAll);
	}

	private List<DictBiz> getFlowSort() {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		return bizClient.getListByLang("wf_schedule", currentLanguage).getData();
	}
}
