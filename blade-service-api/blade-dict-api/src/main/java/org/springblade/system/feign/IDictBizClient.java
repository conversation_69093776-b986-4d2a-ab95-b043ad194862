/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;


import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME,
	fallback = IDictBizClientFallback.class
)
public interface IDictBizClient {

	String API_PREFIX = "/client";
	String GET_BY_ID = API_PREFIX + "/dict-biz/get-by-id";
	String GET_VALUE = API_PREFIX + "/dict-biz/get-value";
	String GET_VALUE_LANG = API_PREFIX + "/dict-biz/get-value-language";
	String GET_LIST = API_PREFIX + "/dict-biz/get-list";
	String GET_LIST_LANG = API_PREFIX + "/dict-biz/get-list-language";
	String GET_LIST_BY_PCODE = API_PREFIX + "/dict-biz/get-list-by-parentCode";
	String GET_ALL_LIST = API_PREFIX + "/dict-biz/get-all-list";
	String BATCH_GET_LIST = API_PREFIX + "/dict-biz/batch-get-list";
	String QUERY_CHILD_BY_DICT_KEY = API_PREFIX + "/dict-biz/queryChildByDictKey";

	String GET_DATA_BY_CODE_KEY=API_PREFIX+"/dict-biz/getBizDataByKeyAndCode";
	/**
	 * 获取字典实体
	 *
	 * @param id 主键
	 * @return
	 */
	@GetMapping(GET_BY_ID)
	R<DictBiz> getById(@RequestParam("id") Long id);

	/**
	 * 获取字典表对应值
	 *
	 * @param code    字典编号
	 * @param dictKey 字典序号
	 * @return
	 */
	@GetMapping(GET_VALUE)
	R<String> getValue(@RequestParam("code") String code, @RequestParam("dictKey") String dictKey);

	/**
	 * 获取字典表
	 *
	 * @param code 字典编号
	 * @return
	 */
	@GetMapping(GET_LIST)
	R<List<DictBiz>> getList(@RequestParam("code") String code);

	@GetMapping(GET_LIST_LANG)
	R<List<DictBiz>> getListByLang(@RequestParam("code") String code,@RequestParam("language") String language);

	/**
	 * 获取字典表
	 *
	 * @param code 字典编号
	 * @return
	 */
	@GetMapping(GET_LIST_BY_PCODE)
	R<List<DictBiz>> getListByParentCode(@RequestParam("code") String code,@RequestParam("parentCode") String parentCode,@RequestParam("language") String language);

	@GetMapping(GET_ALL_LIST)
	R<List<DictBiz>> getListAllLang(@RequestParam("code") String code);

	@GetMapping(GET_VALUE_LANG)
	R<String> getValueByLang(@RequestParam("code") String code,@RequestParam("dictKey") String dictKey,@RequestParam("language") String language);

	@GetMapping(BATCH_GET_LIST)
	R<Map<String,List<DictBiz>>> batchGetList(@RequestParam("codeList") List<String> codeList);

	@GetMapping(QUERY_CHILD_BY_DICT_KEY)
	R<List<DictBiz>> queryChildByDictKey(@RequestParam("code")String code, @RequestParam("dictKey") String dictKey,
										 @RequestParam("language") String language);

	/**
	 * 获取字典表对应值
	 *
	 * @param code    字典编号
	 * @param dictKey 字典序号
	 * @return
	 */
	@GetMapping(GET_DATA_BY_CODE_KEY)
	R<List<DictBiz>> getDataByCodeAndKey(@RequestParam("code") String code, @RequestParam("dictKey") String dictKey);
}
