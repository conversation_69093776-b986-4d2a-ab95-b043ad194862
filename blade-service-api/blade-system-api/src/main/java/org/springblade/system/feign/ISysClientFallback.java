/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.*;
import org.springblade.system.vo.ChangeUserVO;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Feign失败配置
 *
 * <AUTHOR>
 */
@Component
public class ISysClientFallback implements ISysClient {

	@Override
	public R<Menu> getMenu(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Dept> getDept(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getDeptIds(String tenantId, String deptNames) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getDeptIdsByFuzzy(String tenantId, String deptNames) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getDeptName(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<String>> getDeptNames(String deptIds) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<Dept>> getDeptChild(Long deptId) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Boolean> submit(Dept dept) {
		return R.fail("新增部门失败");
	}

	@Override
	public R<Boolean> removeDept(String ids) {
		return R.fail("删除部门失败");
	}

	@Override
	public R<Post> getPost(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getPostIds(String tenantId, String postNames) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getPostIdsByFuzzy(String tenantId, String postNames) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getPostName(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<String>> getPostNames(String postIds) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Role> getRole(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getRoleIds(String tenantId, String roleNames) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getRoleName(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getRoleAlias(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<String>> getRoleNames(String roleIds) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<String>> getRoleAliases(String roleIds) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Role> getRoleInfoByCode(String roleCode) {
		return R.fail("获取角色信息失败");
	}

	@Override
	public R<Tenant> getTenant(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Tenant> getTenant(String tenantId) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<TenantPackage> getTenantPackage(String tenantId) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Param> getParam(Long id) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getParamValue(String paramKey) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Region> getRegion(String code) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<Region>> getRegionList(List<String> codeList) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> getRedisUniqueId(RedisSeqEnums redisSeqEnums) {
		return R.fail("获取redis生成唯一序列id失败");
	}

	@Override
	public R clearRedisSequence() {
		return R.fail("清理redis生成的唯一序列id失败");
	}

	@Override
	public R<List<Role>> getRoleList(@RequestParam("roleIds") String roleIds) {
		return R.fail("获取角色失败");
	}


	@Override
	public R<List<Role>> findUserRoleInfoByUserId(String userId) {
		return R.fail("根据用户id获取角色列表失败");
	}

	@Override
	public R<List<Role>> getRoleIgnoreTenant(@RequestParam("roleCodeList") List<String> roleCodeList, @RequestParam("tenantId") String tenantId) {
		return R.fail("获取角色失败");
	}

	@Override
	public R<JSONObject> alarmConfigList(String language, String roleNames, String deptIds) {
		return R.fail("获取配置失败");
	}

	@Override
	public R<ChangeUserVO> userFind(@RequestParam("areaCode") String areaCode, @RequestParam("phone") String phoneNumber,
									@RequestParam("email") String email, @RequestParam("password") String password) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<ChangeUserVO> findUser(String areaCode, String phoneNumber, String email, String password) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> userFindTest(String areaCode) {
		return R.fail("获取数据失败");
	}
}
