package org.springblade.system.feign;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.vo.UserBatchVO;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserInfo;
import org.springblade.system.entity.UserOauth;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-03-12 11:27
 **/
@Component
@Slf4j
public class UserClientFactory implements FallbackFactory<IUserClient> {
	@Override
	public IUserClient create(Throwable cause) {
		log.info("远程调用IUserClient方法异常，异常信息如下:" + cause.getMessage());
		return new IUserClient() {
			@Override
			public R<User> userInfoById(Long userId) {
				return R.fail("fail");
			}

			@Override
			public R<User> userByAccount(String tenantId, String account) {
				return R.fail("fail");
			}

			@Override
			public R<UserInfo> userInfo(String tenantId, String account) {
				return R.fail("fail");
			}

			@Override
			public R<UserInfo> userInfo(String tenantId, String account, String userType,String phoneDiallingCode) {
				return R.fail("fail");
			}

			@Override
			public R<UserInfo> userAuthInfo(UserOauth userOauth) {
				return R.fail("fail");
			}

			@Override
			public R<Boolean> saveUser(User user) {
				return R.fail("fail");
			}

			@Override
			public R<Boolean> removeUser(String tenantIds) {
				return R.fail("fail");
			}

			@Override
			public R<UserInfo> userByLoginName(String tenantId, String loginName, String loginType, String phoneDiallingCode) {
				return R.fail("fail");
			}

			@Override
			public R deleteUserById(Long userId) {
				return R.fail("fail");
			}

			@Override
			public R<User> userByUserInfo(User user) {
				return R.fail("fail");
			}

			@Override
			public R<List<User>> getAllUsers() {
				return R.fail("fail");
			}

			@Override
			public R<List<User>> getUserList(User user) {
				return R.fail("fail");
			}

			@Override
			public R synchronousUserList(UserBatchVO<User> userUserBatchVO) {
				throw new RuntimeException(cause.getMessage());
			}

			@Override
			public R synchronousUserListForClient(UserBatchVO<User> userUserBatchVO) {
				throw new RuntimeException(cause.getMessage());
			}

			@Override
			public R<UserInfo> userInfoByWeb(String tenantId, String username, String name, String phoneDiallingCode) {
				return R.fail("fail");
			}
		};
	}
}
