/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.client;

import org.springblade.core.mp.support.BladePage;
import org.skyworth.ess.device.entity.Device24Entity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Device24 Feign接口类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@FeignClient(
    value = "device24"
)
public interface IDevice24Client {

    String API_PREFIX = "/client";
    String TOP = API_PREFIX + "/top";

    /**
     * 获取Device24列表
     *
     * @param current   页号
     * @param size      页数
     * @return BladePage
     */
    @GetMapping(TOP)
    BladePage<Device24Entity> top(@RequestParam("current") Integer current, @RequestParam("size") Integer size);

}
