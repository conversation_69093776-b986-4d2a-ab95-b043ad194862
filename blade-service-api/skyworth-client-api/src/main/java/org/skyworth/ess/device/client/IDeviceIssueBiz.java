package org.skyworth.ess.device.client;


import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(
        value ="skyworth-toolkit",
        fallback = DeviceIssueBizFallBack.class
)
public interface IDeviceIssueBiz {

    String API_PREFIX = "/issue";
    String address = API_PREFIX + "/workMode";
	String getDevice=API_PREFIX+"/getDevice";
	String setting=API_PREFIX+"/setting";

	String time=API_PREFIX+"/getDeviceTime";

	String setDeviceTime=API_PREFIX+"/setDeviceTime";

	String errorInfo=API_PREFIX + "/errorInfo";

    @PostMapping(address)
    Map<String,String> dataIssueToDevice(@RequestBody JSONObject jsonObject);

	@PostMapping(setting)
	Map<String,String> setting(@RequestBody JSONObject jsonObject);

	@PostMapping(time)
	Map<String,String> getDeviceTime(@RequestBody JSONObject jsonObject);

	@PostMapping(setDeviceTime)
	R setDeviceTime(@RequestBody List<JSONObject> jsonObject);

	@PostMapping(errorInfo)
	R erororInfo(@RequestBody List<JSONObject> jsonObject);
}
