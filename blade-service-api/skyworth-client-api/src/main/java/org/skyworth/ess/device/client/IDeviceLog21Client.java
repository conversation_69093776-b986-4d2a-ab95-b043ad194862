package org.skyworth.ess.device.client;

import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.DeviceLog21;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value ="skyworth-client",
        fallback = IDeviceLog21Fallback.class
)
public interface IDeviceLog21Client {

    String API_PREFIX = "/deviceLog";
    String device21 = API_PREFIX + "/21/insert";
	String NETWORK = API_PREFIX + "/21/insert/network";
	String GETDEVICE21_BY_PLANT_ID = API_PREFIX + "/21/getDevice21ByPlantId";

    @PostMapping(device21)
    void insertBatchDeviceLog21Column(@RequestBody List<DeviceLog21> deviceLog21List);

	@PostMapping(NETWORK)
	void insertBatchDeviceLog21ForNetWork(@RequestBody List<DeviceLog21> deviceLog21List);

	@PostMapping(GETDEVICE21_BY_PLANT_ID)
	Device21Entity getDevice21ByPlantId(@RequestParam("plantId") long plantId,@RequestParam("deviceSerialNumber") String deviceSerialNumber);
}
