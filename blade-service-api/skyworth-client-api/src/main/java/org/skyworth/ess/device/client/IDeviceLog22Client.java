package org.skyworth.ess.device.client;

import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.DeviceLog21;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value ="skyworth-client",
        fallback = IDeviceLog22Fallback.class
)
public interface IDeviceLog22Client {

    String API_PREFIX = "/deviceLog";
    String device22 = API_PREFIX + "/22/list";

    @PostMapping(device22)
	List<DeviceLog22> queryDeviceLog22list(@RequestBody List<DeviceLog22> deviceLog22List);

}
