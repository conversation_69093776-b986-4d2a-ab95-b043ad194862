package org.skyworth.ess.device.client;

import org.skyworth.ess.device.entity.DeviceLog23;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        value ="skyworth-client",
        fallback = IDeviceLog23Fallback.class
)
public interface IDeviceLog23Client {

    String API_PREFIX = "/deviceLog";
    String device23 = API_PREFIX + "/23/insert";
	String NETWORK = API_PREFIX + "/23/insert/network";

    @PostMapping(device23)
    void insertBatchDeviceLog23Column(@RequestBody List<DeviceLog23> deviceLog23List);

	@PostMapping(NETWORK)
	void insertBatchDeviceLog23ForNetWork(@RequestBody List<DeviceLog23> deviceLog23List);

}
