package org.skyworth.ess.device.client;

import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(
        value ="skyworth-client",
        fallback = IErrorInfoClientCallBack.class
)
public interface IErrorInfoClient {


    String API_PREFIX = "/event";
    String address = API_PREFIX + "/errorInfo";

    @PostMapping(address)
    R<String> errorInfo(@RequestBody Map<String, Map<String,Object>> error);
}
