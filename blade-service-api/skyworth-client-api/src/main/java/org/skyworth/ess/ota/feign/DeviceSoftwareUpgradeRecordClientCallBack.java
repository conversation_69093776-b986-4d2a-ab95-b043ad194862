/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author:
 * @since:
 **/
@Component
public class DeviceSoftwareUpgradeRecordClientCallBack implements IDeviceSoftwareUpgradeRecordClient{
	@Override
	public JSONObject getById(Long id) {
      return new JSONObject();
	}
}
