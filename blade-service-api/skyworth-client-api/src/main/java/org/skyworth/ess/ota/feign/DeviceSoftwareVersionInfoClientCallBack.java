/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-26 14:06
 **/
@Component
public class DeviceSoftwareVersionInfoClientCallBack implements IDeviceSoftwareVersionInfoClient{
	@Override
	public void obtainUpgradeResults(Map<String, String> map) {

	}

	@Override
	public void obtainUpgradePushResults(List<String> deviceSns) {

	}

	@Override
	public void publishOtaUpgradeResultToApp(Map<String, Object> map) {

	}

	@Override
	public void otaOfflineUpgradeException(JSONObject jsonObject) {

	}
}
