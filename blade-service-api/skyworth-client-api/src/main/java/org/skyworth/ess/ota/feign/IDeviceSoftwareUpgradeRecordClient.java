/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.constant.CientConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 *  Feign接口类
 *
 * <AUTHOR> @since
 */
@FeignClient(
	value = CientConstant.APPLICATION_CLIENT_NAME,
	fallback = DeviceSoftwareUpgradeRecordClientCallBack.class
)
public interface IDeviceSoftwareUpgradeRecordClient {

	String API_PREFIX = "/ota";
	String GET_BY_ID = API_PREFIX + "/getById";

	@PostMapping(GET_BY_ID)
	JSONObject getById(@RequestBody Long id);
}
