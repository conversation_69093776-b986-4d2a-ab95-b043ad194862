/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.constant.CientConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 设备软件版本信息表 Feign接口类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@FeignClient(
	value = CientConstant.APPLICATION_CLIENT_NAME,
	fallback = DeviceSoftwareVersionInfoClientCallBack.class
)
public interface IDeviceSoftwareVersionInfoClient {

	String API_PREFIX = "/ota";
	String OBTAIN_UPGRADE_RESULTS_ADDRESS = API_PREFIX + "/obtainUpgradeResults";
	String OBTAIN_UPGRADE_PUSH_RESULTS_ADDRESS = API_PREFIX + "/obtainUpgradePushResults";
	String PUBLISH_OTA_UPGRADE_RESULT_TO_APP = API_PREFIX + "/publishOtaUpgradeResultToApp";
	String OTA_OFFLINE_UPGRADE_EXCEPTION = API_PREFIX + "/otaOfflineUpgradeException";

	/**
	 * 获取最终升级结果
	 *
	 * @param map 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:36
	 **/
	@PostMapping(OBTAIN_UPGRADE_RESULTS_ADDRESS)
	void obtainUpgradeResults(@RequestBody Map<String, String> map);

	/**
	 * 获取包推送结果
	 *
	 * @param deviceSns 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:40
	 **/
	@PostMapping(OBTAIN_UPGRADE_PUSH_RESULTS_ADDRESS)
	void obtainUpgradePushResults(@RequestBody List<String> deviceSns);


	@PostMapping(PUBLISH_OTA_UPGRADE_RESULT_TO_APP)
	void publishOtaUpgradeResultToApp(@RequestBody Map<String, Object> map);

	/**
	 * 脱机异常回写
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/3/5 17:43
	 **/
	@PostMapping(OTA_OFFLINE_UPGRADE_EXCEPTION)
	void otaOfflineUpgradeException(@RequestBody JSONObject jsonObject);
}
