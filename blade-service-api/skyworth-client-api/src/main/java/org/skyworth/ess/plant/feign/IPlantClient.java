package org.skyworth.ess.plant.feign;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.constant.CientConstant;
import org.skyworth.ess.plant.vo.PlantAgentViewVO;
import org.skyworth.ess.plant.vo.PlantClientVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
	value = CientConstant.APPLICATION_CLIENT_NAME,
	fallback = PlantClientCallBack.class
)
public interface IPlantClient {
	String PLANT = "/plant";
	String PLANT_BY_USER_INFO = PLANT + "/feign/plantByUserInfo";

	String PLANT_AGENT_VIEW_INFO = PLANT + "/feign/plantAgentViewInfo";

	String PLANT_QUERY_UNAUTHORIZED_PERSONNEL = PLANT + "/feign/queryUnauthorizedUser";
	String CLEAN_PLANT_OPERATION_USER_ID = PLANT + "/feign/cleanPlantOperationUserId";
	String CLEAN_PLANT_DEPT_ID_OR_USERS = PLANT + "/feign/cleanPlantDeptIdOrUsers";
	String UPDATE_PLANT = PLANT + "/feign/updatePlant";

	/**
	 * 根据站点
	 *
	 * @param vo
	 * @return Menu
	 */
	@PostMapping(PLANT_BY_USER_INFO)
	R<List<PlantClientVO>> getPlantByUserInfo(@RequestBody PlantClientVO vo);

	/**
	 * 代理商管理查询代理商绑定的站点信息
	 *
	 * @param query  分页参数
	 * @param deptId 入参
	 * @return R<List < PlantAgentViewVO>>
	 * <AUTHOR>
	 * @since 2024/3/7 16:49
	 **/
	@GetMapping(PLANT_AGENT_VIEW_INFO)
	R<IPage<PlantAgentViewVO>> getPlantAgentViewInfo(@RequestParam("deptId") Long deptId, Query query);

	/**
	 * 查询站点下没权限查询的用户id
	 *
	 * @param plantId 入参
	 * @return R<List < Long>>
	 * <AUTHOR>
	 * @since 2024/3/18 16:39
	 **/
	@GetMapping(PLANT_QUERY_UNAUTHORIZED_PERSONNEL)
	R<List<Long>> queryUnauthorizedUser(@RequestParam("plantId") Long plantId);

	@GetMapping(CLEAN_PLANT_OPERATION_USER_ID)
	R<Boolean> cleanPlantOperationUserId(@RequestParam("userIdList") List<Long> userIdList);

	/**
	 * type：0，清理代理商管理人员同步清理站点的运维人员。
	 * type:1 批量删除代理商，同步清理代理商和代理商下面的运维人员。
	 *
	 * @param jsonObject 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2024/3/21 13:42
	 **/
	@PostMapping(CLEAN_PLANT_DEPT_ID_OR_USERS)
	R<Boolean> cleanPlantDeptIdOrUsers(@RequestBody JSONObject jsonObject);

	@PostMapping(UPDATE_PLANT)
	R<Integer> updatePlant(@RequestBody JSONObject jsonObject);
}
