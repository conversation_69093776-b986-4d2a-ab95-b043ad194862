package org.skyworth.ess.plant.feign;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.plant.vo.PlantAgentViewVO;
import org.skyworth.ess.plant.vo.PlantClientVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
public class PlantClientCallBack implements IPlantClient {
	@Override
	public R<List<PlantClientVO>> getPlantByUserInfo(PlantClientVO vo) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<IPage<PlantAgentViewVO>> getPlantAgentViewInfo(Long deptId, @RequestBody Query query) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<List<Long>> queryUnauthorizedUser(Long plantId) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Boolean> cleanPlantOperationUserId(@RequestParam("userIdList") List<Long> userIdList) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Boolean> cleanPlantDeptIdOrUsers(JSONObject jsonObject) {
		return R.fail("获取数据失败");
	}

	@Override
	public R<Integer> updatePlant(JSONObject jsonObject) {
		return R.fail("获取数据失败");
	}
}
