package org.skyworth.ess.plant.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "站点运维信息VO", description = "站点运维信息VO")
public class PlantOperationInfoVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "站点id")
	@NotNull(message = "站点id不能为空")
	private Long plantId;

	@ApiModelProperty(value = "运维团队，代理商公司dept_id")
	private Long operationCompanyDeptId;

	@ApiModelProperty(value = "运维人员，代理商公司员工用户id")
	private Long operationUserId;

	@ApiModelProperty(value = "安装日期")
	private Date installDate;

	@ApiModelProperty(value = "安装团队")
	private Long installTeamId;
}
