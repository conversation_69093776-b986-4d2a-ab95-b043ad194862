package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.i18n.constant.Constant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class TimeZoneCachedClientCallBack implements ITimeZoneCachedClient {

	@Override
	public void putCache(String plantId, String timeZone) {

	}

	@Override
	public Map<String, String> getAllCache(Set<String> plantIdSet) {
		return Map.of();
	}

	@Override
	public void removeAllCache(Set<String> plantIdSet) {

	}

	@Override
	public String getCache(Long plantId) {
		return "";
	}

	//@Override
	//public String baseEnergyKafkaHandler(List<JSONObject> jsonObjectList) {
		//return "";
	//}
}
