/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.ota.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-09-22 11:00
 **/
@Data
public class DistributeUpgradeVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 设备编号
	 */
	private String deviceSn;

	/**
	 * 升级包路径
	 */
	private List<JSONObject> otaUpdatePackVOList;

	/**
	 * 当前时间
	 */
	private Date timestamp;

	/**
	 * 消息请求id
	 */
	private String requestId;
}
