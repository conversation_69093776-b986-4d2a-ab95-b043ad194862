/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service;


import org.springblade.core.mp.base.BaseService;
import org.springblade.system.entity.User;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * 用户查询服务类
 *
 * <AUTHOR>
 */
public interface IUserSearchService extends BaseService<User> {

	/**
	 * 根据用户ID查询用户列表
	 *
	 * @param userId 用户ID
	 * @return 用户列表
	 */
	List<User> listByUser(List<Long> userId);

	/**
	 * 根据部门ID查询用户列表
	 *
	 * @param deptId 部门ID
	 * @return 用户列表
	 */
	List<User> listByDept(List<Long> deptId);

	/**
	 * 根据岗位ID查询用户列表
	 *
	 * @param postId 岗位ID
	 * @return 用户列表
	 */
	List<User> listByPost(List<Long> postId);

	/**
	 * 根据角色ID查询用户列表
	 *
	 * @param roleId 角色ID
	 * @return 用户列表
	 */
	List<User> listByRole(List<Long> roleId);

	/**
	 * 根据用户ID查询所有用户列表（包含删除的，工作流用）
	 *
	 * @param userId 用户ID
	 * @return 用户列表
	 */
	List<User> listAllByUser(List<Long> userId);

	/**
	 * 便携式查询app注册用户信息
	 *
	 * @param map 入参
	 * @return List<UserStatisticsVO>
	 * <AUTHOR>
	 * @since 2024/1/27 10:06
	 **/
	List<UserStatisticsVO> singleDayRegisterInfo(Map<String, Object> map);

	/**
	 * 便携式查询app注册用户信息
	 *
	 * @param map 入参
	 * @return List<UserStatisticsVO>
	 * <AUTHOR>
	 * @since 2024/1/27 10:06
	 **/
	List<UserRegistrationVO> singleDayRegisterExcelInfo(Map<String, Object> map);

	List<User> selectMappingUser(List<Long> userIds);

	List<User> selectMappingSourceUser(List<Long> userIds);

	List<User> selectUserByPhone(String phone);

	/**
	 * 根据租户ID和部门ID查询用户下拉框
	 *
	 * @param deptIds   部门ID
	 * @param tenantId 租户ID
	 * @param plantId 站点id
	 * @return R<List < User>>
	 * <AUTHOR>
	 * @since 2024/3/11 10:55
	 **/
	List<User> listTenantUserByDept(Long deptIds, Long plantId,String tenantId);

}
