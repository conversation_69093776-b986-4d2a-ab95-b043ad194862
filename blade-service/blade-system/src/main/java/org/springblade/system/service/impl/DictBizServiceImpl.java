/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.mapper.DictBizMapper;
import org.springblade.system.service.IDictBizService;
import org.springblade.system.vo.DictBizVO;
import org.springblade.system.wrapper.DictBizWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.core.cache.constant.CacheConstant.DICT_CACHE;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DictBizServiceImpl extends ServiceImpl<DictBizMapper, DictBiz> implements IDictBizService {

	@Override
	public List<DictBizVO> tree() {
		return ForestNodeMerger.merge(baseMapper.tree());
	}

	@Override
	public List<DictBizVO> parentTree() {
		return ForestNodeMerger.merge(baseMapper.parentTree());
	}

	@Override
	public String getValue(String code, String dictKey) {
		return Func.toStr(baseMapper.getValue(code, dictKey), StringPool.EMPTY);
	}

	@Override
	public List<DictBiz> getList(String code) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		return baseMapper.getList(code, currentLanguage);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(DictBiz dict) {
		LambdaQueryWrapper<DictBiz> lqw = Wrappers.<DictBiz>query().lambda()
				.eq(DictBiz::getCode, dict.getCode())
				.eq(DictBiz::getLanguage, dict.getLanguage())
				.eq(DictBiz::getParentId, dict.getParentId());
		if ("device_battery_match".equals(dict.getCode())) {
			lqw.eq(DictBiz::getDictKey, dict.getDictKey());
			lqw.eq(DictBiz::getDictValue, dict.getDictValue());
		} else {
			lqw.eq(DictBiz::getDictKey, dict.getDictKey());
		}
		Long cnt = baseMapper.selectCount((Func.isEmpty(dict.getId())) ? lqw : lqw.notIn(DictBiz::getId, dict.getId()));
		if (cnt > 0L) {
			throw new BusinessException("system.dict.key.value.exist");
		}
		// 修改顶级字典后同步更新下属字典的编号
		if (Func.isNotEmpty(dict.getId()) && dict.getParentId().longValue() == BladeConstant.TOP_PARENT_ID) {
			DictBiz parent = DictBizCache.getById(dict.getId());
			this.update(Wrappers.<DictBiz>update().lambda().set(DictBiz::getCode, dict.getCode()).eq(DictBiz::getCode, parent.getCode()).ne(DictBiz::getParentId, BladeConstant.TOP_PARENT_ID));
		}
		if (Func.isEmpty(dict.getParentId())) {
			dict.setParentId(BladeConstant.TOP_PARENT_ID);
		}
		dict.setIsDeleted(BladeConstant.DB_NOT_DELETED);
		CacheUtil.clear(DICT_CACHE);
		return saveOrUpdate(dict);
	}

	@Override
	public boolean removeDict(String ids) {
		Long cnt = baseMapper.selectCount(Wrappers.<DictBiz>query().lambda().in(DictBiz::getParentId, Func.toLongList(ids)));
		if (cnt > 0L) {
			throw new BusinessException("system.exception.delete.child.nodes");
		}
		return removeByIds(Func.toLongList(ids));
	}

	@Override
	public IPage<DictBizVO> parentList(Map<String, Object> dict, Query query) {
		IPage<DictBiz> page = this.page(Condition.getPage(query), Condition.getQueryWrapper(dict, DictBiz.class).lambda().eq(DictBiz::getParentId, CommonConstant.TOP_PARENT_ID).orderByAsc(DictBiz::getSort).orderByAsc(DictBiz::getId));
		return DictBizWrapper.build().pageVO(page);
	}

	@Override
	public List<DictBizVO> childList(Map<String, Object> dict, Long parentId) {
		if (parentId < 0) {
			return new ArrayList<>();
		}
		dict.remove("parentId");
		DictBiz parentDict = DictBizCache.getById(parentId);
		List<DictBiz> list = this.list(Condition.getQueryWrapper(dict, DictBiz.class).lambda().ne(DictBiz::getId, parentId).eq(DictBiz::getCode, parentDict.getCode()).orderByAsc(DictBiz::getSort).orderByAsc(DictBiz::getId));
		return DictBizWrapper.build().listNodeVO(list);
	}

	@Override
	public List<DictBiz> getListAllLang(String code) {
		return baseMapper.getListAllLang(code);
	}

	@Override
	public List<DictBiz> getListByParentCode(String code, String parentCode, String language) {
		return baseMapper.getListByParentCode(code, parentCode,language);
	}

	@Override
	public List<DictBiz> getListByLang(String code, String language) {
		return baseMapper.getList(code, language);
	}

	@Override
	public String getValueByLang(String code, String dictKey, String language) {
		return baseMapper.getValueByLang(code, dictKey, language);
	}

	@Override
	public Map<String, List<DictBiz>> batchGetList(List<String> codeList) {
		if (CollectionUtil.isEmpty(codeList)) {
			return new HashMap<>();
		}
		List<DictBiz> dictBizList = baseMapper.batchGetList(codeList);
		if (CollectionUtil.isEmpty(dictBizList)) {
			return new HashMap<>();
		}
		return dictBizList.stream().collect(Collectors.groupingBy(DictBiz::getCode));
	}

	@Override
	public List<DictBiz> queryChildByDictKey(String code, String dictKey, String language) {
		List<DictBiz> dictBizList = baseMapper.queryChildByDictKey(code, dictKey, language);
		if (CollectionUtil.isEmpty(dictBizList)) {
			return new ArrayList<>();
		}
		return dictBizList;
	}

	@Override
	public List<DictBiz> getDataByCodeAndKey(String code, String key) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		return baseMapper.getDataByCodeAndKey(code, key, currentLanguage);
	}
	@Override
	public JSONObject alarmConfigList(String language, String roleNames, String deptIds){
		List<DictBiz> dictBizList =
			DictBizCache.getListByLanguage(DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode(),
				language);
		JSONObject jsonResult = new JSONObject();
		if(dictBizList == null){
			return jsonResult;
		}
		// 获取根节点ID
		DictBiz dictBiz = super.getOne(Wrappers.<DictBiz>lambdaQuery().eq(DictBiz::getIsDeleted,
				BladeConstant.DB_NOT_DELETED)
			.eq(DictBiz::getCode, DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode())
			.eq(DictBiz::getParentId, BizConstant.NUMBER_ZERO)
			.select(DictBiz::getId));
		if (dictBiz == null) {
			return jsonResult;
		}
		JSONArray jsonArray = new JSONArray();
		// 获取1级节点
		Map<Long,String> dictLevel1CodeAndIdMap =
			Optional.of(dictBizList).orElse(new ArrayList<>()).stream().filter(a-> Objects.equals(a.getParentId(), dictBiz.getId())).collect(Collectors.toMap(DictBiz::getId, DictBiz::getDictKey, (a, b) -> a));
		dictBizList.forEach(a-> {
			Long parentId = a.getParentId();
			if (dictLevel1CodeAndIdMap.containsKey(parentId)) {
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("code",convertCode(dictLevel1CodeAndIdMap.get(parentId)) + "-" + a.getDictKey());
				jsonObject.put("user",a.getAttribute2());
				jsonObject.put("agent",a.getAttribute3());
				jsonObject.put("admin",CommonConstant.FLAG_Y);
				jsonObject.put("abnormalDesc",a.getDictValue());
				jsonArray.add(jsonObject);
			}
		});
		jsonResult.put("data",jsonArray);
		jsonResult.put("roleType", StatusDisplayUtil.getRoleType(roleNames, deptIds));
		return jsonResult;
	}

	/**
	 * 编码转换为app使用的编码
	 * @param  code 入参
	 * @return String
	 * <AUTHOR>
	 * @date   2025/4/18 18:48
	 */
	private String convertCode(String code){
		String encodingAfterConversion = "";
		switch (code){
			case "101E":
				encodingAfterConversion = "1";
				break;
			case "101F":
				encodingAfterConversion = "2";
				break;
			case "1020":
				encodingAfterConversion = "3";
				break;
			case "2013":
				encodingAfterConversion = "4";
				break;
			case "2213":
				encodingAfterConversion = "5";
				break;
			case "301F":
				encodingAfterConversion = "6";
				break;
			default:
				encodingAfterConversion = "7";
				break;
		}
		return encodingAfterConversion;
	}
}
