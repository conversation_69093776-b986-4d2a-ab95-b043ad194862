/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.common.constant.BizConstant;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.Region;
import org.springblade.system.excel.RegionExcel;
import org.springblade.system.mapper.RegionMapper;
import org.springblade.system.service.IRegionService;
import org.springblade.system.vo.RegionVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.springblade.system.cache.RegionCache.*;


/**
 * 行政区划表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements IRegionService {

	@Override
	public boolean submit(Region region) {
		// 设置市级编号格式
		String regionCode = region.getCode();
		String regionParentCode = region.getParentCode();
		if (regionCode.startsWith(MAIN_CODE)) {
			region.setCode(StringUtil.removePrefix(regionCode, MAIN_CODE));
		}
		if (regionParentCode.startsWith(MAIN_CODE)) {
			region.setParentCode(StringUtil.removePrefix(regionParentCode, MAIN_CODE));
		}
		// 查询是否已存在
		Long cnt = baseMapper.selectCount(Wrappers.<Region>query().lambda().eq(Region::getCode, region.getCode()));
		if (cnt > 0L) {
			setRegionNameEn(region);
			return this.updateById(region);
		}
		// 设置祖区划编号
		Region parent = getByCode(region.getParentCode());
		if (parent != null && (Func.isNotEmpty(parent) || Func.isNotEmpty(parent.getCode()))) {
			String ancestors = parent.getAncestors() + StringPool.COMMA + parent.getCode();
			region.setAncestors(ancestors);
		}

		// 设置省、市、区、镇、村
		Integer level = region.getRegionLevel();
		if (level == COUNTRY_LEVEL){
			region.setParentCode(BizConstant.CHAR_ZERO);
			region.setAncestors(BizConstant.CHAR_ZERO);
		}
		String code = region.getCode();
		String name = region.getName();
		if (level == PROVINCE_LEVEL) {
			region.setProvinceCode(code);
			region.setProvinceName(name);
		} else if (level == CITY_LEVEL) {
			region.setCityCode(code);
			region.setCityName(name);
		} else if (level == DISTRICT_LEVEL) {
			region.setDistrictCode(code);
			region.setDistrictName(name);
		} else if (level == TOWN_LEVEL) {
			region.setTownCode(code);
			region.setTownName(name);
		} else if (level == VILLAGE_LEVEL) {
			region.setVillageCode(code);
			region.setVillageName(name);
		}
		return this.save(region);
	}

	private static void setRegionNameEn(Region region) {
		// 设置省、市、区、镇、村
		Integer level = region.getRegionLevel();
		String nameEn = region.getNameEn();
		if (level == PROVINCE_LEVEL) {
			region.setProvinceNameEn(nameEn);
		} else if (level == CITY_LEVEL) {
			region.setCityNameEn(nameEn);
		} else if (level == DISTRICT_LEVEL) {
			region.setDistrictNameEn(nameEn);
		} else if (level == TOWN_LEVEL) {
			region.setTownNameEn(nameEn);
		} else if (level == VILLAGE_LEVEL) {
			region.setVillageNameEn(nameEn);
		}
	}

	@Override
	public boolean removeRegion(String id) {
		Long cnt = baseMapper.selectCount(Wrappers.<Region>query().lambda().eq(Region::getParentCode, id));
		if (cnt > 0L) {
			throw new BusinessException("system.exception.delete.child.nodes");
		}
		return removeById(id);
	}

	@Override
	public List<RegionVO> lazyList(String parentCode, Map<String, Object> param) {
		return baseMapper.lazyList(parentCode, param);
	}

	@Override
	public List<RegionVO> lazyTree(String parentCode, Map<String, Object> param) {
		return baseMapper.lazyTree(parentCode, param);
	}

	@Override
	public void importRegion(List<RegionExcel> data, Boolean isCovered) {
		List<Region> list = new ArrayList<>();
		data.forEach(regionExcel -> {
			Region region = BeanUtil.copy(regionExcel, Region.class);
			list.add(region);
		});
		if (isCovered) {
			this.saveOrUpdateBatch(list);
		} else {
			this.saveBatch(list);
		}
	}

	@Override
	public List<RegionExcel> exportRegion(Wrapper<Region> queryWrapper) {
		return baseMapper.exportRegion(queryWrapper);
	}

	@Override
	public List<Region> getRegionList(List<String> codeList) {
		return baseMapper.getRegionList(codeList);
	}
}
