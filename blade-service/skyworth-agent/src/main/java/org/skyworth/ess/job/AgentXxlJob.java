package org.skyworth.ess.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.system.feign.ISysClient;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-11-23 15:37
 **/
@Component
@Slf4j
@AllArgsConstructor
public class AgentXxlJob {
	private ISysClient sysClient;
	private final BladeRedis bladeRedis;
	/**
	 * 清理订单号
	 *
	 * @param param 入参
	 * @return ReturnT<String>
	 * <AUTHOR>
	 * @since 2023/9/22 10:03
	 **/
	@XxlJob("clearRedisSequence")
	public ReturnT<String> clearRedisSequence(String param) throws Exception {
		sysClient.clearRedisSequence();
		return ReturnT.SUCCESS;
	}

	@XxlJob("deleteUserBusinessData")
	public ReturnT<String> deleteUserBusinessData(String param) throws Exception {
		Object o = bladeRedis.rPop(CommonConstant.PHONE_DELETE_USER_COMMON_KEY + CommonConstant.APP_TYPE_AGENT + StringPool.COLON);
		log.info("deleteUserBusinessData :{}",o);
		return ReturnT.SUCCESS;
	}
}
