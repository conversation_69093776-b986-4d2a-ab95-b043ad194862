package org.skyworth.ess.job;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class EhsRejectJob {


	private IOrderWorkFlowService iOrderWorkFlowService;

	private IReviewOrderService iReviewOrderService;

	private final IOrderRelatedUserService iOrderRelatedUserService;


	@XxlJob("ehsRejectData")
	public ReturnT<String> ehsRejectData(String param) {
		//根据条件查询订单流程表qc节点的数据
		List<OrderWorkFlowEntity> orderWorkFlowEntityList = selectOrderWorkFlowData();
		if (CollectionUtils.isEmpty(orderWorkFlowEntityList)) {
			return ReturnT.SUCCESS;
		}
		//订单id
		List<Long> orderIdList = orderWorkFlowEntityList.stream().map(OrderWorkFlowEntity::getOrderId).filter(Objects::nonNull).collect(Collectors.toList());
		//构建审批公共参数
		OrderFlowDTO orderFlowDTO = constructVariablesParam();
		//查询施工人员
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.in(OrderRelatedUserEntity::getOrderId, orderIdList).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()).eq(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		List<OrderRelatedUserEntity> orderRelatedUserEntitiesList = this.iOrderRelatedUserService.list(orderRelatedUserEntityLambdaQueryWrapper);
		if (CollectionUtils.isNotEmpty(orderRelatedUserEntitiesList)) {
			Map<Long, Long> orderUserMap = orderRelatedUserEntitiesList.stream().collect(Collectors.toMap(OrderRelatedUserEntity::getOrderId, OrderRelatedUserEntity::getUserId));
			for (Long orderId : orderIdList) {
				//施工人员
				orderFlowDTO.getVariables().put("siteEngineer", orderUserMap.get(orderId));
				//业务id
				orderFlowDTO.setBusinessId(String.valueOf(orderId));
				//审批流转
				try {
					iReviewOrderService.examineApprove(orderFlowDTO);
				} catch (Exception e) {
					log.error("error order" + orderId + "：" + e.getMessage());
				}
			}
		}
		return ReturnT.SUCCESS;
	}


	/**
	 * @Description: 根据条件查询订单流程表qc ehs节点的数据
	 * @Param: []
	 * @Return: java.util.List<org.skyworth.ess.entity.OrderWorkFlowEntity>
	 * @Author: baixu
	 * @Date: 2024/1/16 18:43
	 **/
	private List<OrderWorkFlowEntity> selectOrderWorkFlowData() {
		LambdaQueryWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderWorkFlowEntityLambdaQueryWrapper.lt(OrderWorkFlowEntity::getUpdateTime, LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
		orderWorkFlowEntityLambdaQueryWrapper.in(OrderWorkFlowEntity::getWfCurrentStatus, "qcSubmission", "ehsVerification");
		orderWorkFlowEntityLambdaQueryWrapper.select(OrderWorkFlowEntity::getOrderId, OrderWorkFlowEntity::getEhsVersion);
		return iOrderWorkFlowService.list(orderWorkFlowEntityLambdaQueryWrapper);
	}


	/**
	 * @Description: 构建审批公共参数
	 * @Param: []
	 * @Return: org.springblade.flow.core.dto.OrderFlowDTO
	 * @Author: baixu
	 * @Date: 2024/1/16 18:45
	 **/
	private OrderFlowDTO constructVariablesParam() {
		OrderFlowDTO orderFlowDTO = new OrderFlowDTO();
		HashMap<String, Object> variablesMap = new HashMap<>();
		//审批状态 1：同意；2：拒绝 ； 3：取消
		variablesMap.put("examineApproveType", "2");
		//下一步处理节点类型 人：user ；角色：role
		variablesMap.put("wfCurrentType", "user");
		//审批意见
		variablesMap.put("comment", "Automatic rejection");
		//时间版本标志
		//variablesMap.put("ehsVersion", null);
		//超时驳回标志
		variablesMap.put("timeOutRejectFlag", "1");
		orderFlowDTO.setVariables(variablesMap);
		return orderFlowDTO;
	}


}
