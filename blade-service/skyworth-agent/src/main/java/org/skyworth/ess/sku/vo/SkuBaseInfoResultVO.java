package org.skyworth.ess.sku.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SkuBaseInfoResultVO implements Serializable {
	private static final long serialVersionUID = 1L;
	private String skuCode;
	/**
	 * 物料名称
	 */
	private String skuName;
	private String skuDeviceType;
	private String skuCompany;
	private String standards;
	private String model;
	private String unit;
	private BigDecimal price;
	private String quantity;
	private String skuDeviceTypeName;
	private String itemCode;//和skuCode一样，兼容 订单上物料查询
}
