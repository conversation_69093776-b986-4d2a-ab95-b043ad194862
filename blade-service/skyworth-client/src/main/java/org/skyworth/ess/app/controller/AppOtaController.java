package org.skyworth.ess.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppOtaService;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.service.IOtaUpdatePackService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description app ota升级
 * @create-time 2024/1/17 18:01:40
 */
@RestController
@RequestMapping("/app/ota")
@Api(value = "app ota接口", tags = "app ota接口")
@Slf4j
@AllArgsConstructor
public class AppOtaController extends BladeController {
	@Resource
	private IAppOtaService appOtaService;

	private final IOtaUpdatePackService otaUpdatePackService;

	@Resource
	private IAppService appService;


	@GetMapping("/queryUpgradeEnable")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询设备是否可以升级", notes = "查询设备是否可以升级")
	@ApiLog("app查询设备是否可以升级")
	public R<JSONObject> queryUpgradeEnable(@ApiIgnore @RequestParam Map<String, Object> deviceSoftwareVersionInfo, Query query) {
		JSONObject jsonObject = appOtaService.queryUpgradeEnable(deviceSoftwareVersionInfo, query);
		return R.data(jsonObject);
	}

	@PostMapping("/issueUpgrade")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "app下发ota升级", notes = "app下发ota升级")
	@ApiLog("app下发ota升级")
	public R issueUpgrade(@RequestBody JSONObject jsonObject) {
		return appOtaService.issueUpgrade(jsonObject);
	}


	/**
	 * 查询当前app的版本信息
	 * type=1 用户app
	 * type=2 代理商app
	 * phoneType=1 苹果  phoneType=2 android
	 */
	@GetMapping("/currentVersion")
	@ApiOperation(value = "获取app当前版本", notes = "获取app当前版本")
	@ApiLog("获取app当前版本")
	public R<OtaUpdatePackEntity> getAppCurrentVersion(@RequestParam("type") String type, @RequestParam("phoneType") String phoneType) {
		String bigType = "app";
		String smallType = "";
		if ("1".equals(type)) {
			if ("1".equals(phoneType)) {
				smallType = "user-app-ios";
			} else {
				smallType = "user-app-android";
			}
		} else {
			if ("1".equals(phoneType)) {
				smallType = "agent-app-ios";
			} else {
				smallType = "agent-app-android";
			}
		}
		LambdaQueryWrapper<OtaUpdatePackEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OtaUpdatePackEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		queryWrapper.eq(OtaUpdatePackEntity::getBigType, bigType);
		queryWrapper.eq(OtaUpdatePackEntity::getSmallType, smallType);
		queryWrapper.eq(OtaUpdatePackEntity::getIsNewVersion, 1);
		queryWrapper.orderByDesc(OtaUpdatePackEntity::getUpdateTime);
		queryWrapper.last("limit 1");
		OtaUpdatePackEntity otaUpdatePackEntity = otaUpdatePackService.getOne(queryWrapper);

		return R.data(otaUpdatePackEntity);
	}

	@TenantIgnore
	@GetMapping("/upgrading")
	@ApiOperation(value = "app软件升级", notes = "app软件升级")
	@ApiLog("app软件升级")
	public R<OtaUpdatePackEntity> upgrading(@ApiIgnore @RequestParam Map<String, Object> map) {
		return R.data(appService.upgrading(map));
	}

}
