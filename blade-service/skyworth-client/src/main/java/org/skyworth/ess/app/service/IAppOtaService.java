package org.skyworth.ess.app.service;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.app.vo.AppVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.util.Map;

/**
 * <AUTHOR> - x<PERSON><PERSON>jiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/1/18 17:11:06
 */
public interface IAppOtaService {
	JSONObject queryUpgradeEnable(Map<String, Object> deviceSoftwareVersionInfo, Query query);

	R issueUpgrade(JSONObject jsonObjectO);
}
