package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.service.IAppOtaService;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.skyworth.ess.ota.service.IOtaUpdatePackService;
import org.skyworth.ess.ota.vo.DeviceSoftwareVersionInfoVO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/1/18 17:11:21
 */
@Service
@Slf4j
public class AppOtaServiceImpl implements IAppOtaService {

	@Resource
	private IDeviceSoftwareVersionInfoService deviceSoftwareVersionInfoService;
	@Resource
	private IOtaUpdatePackService otaUpdatePackService;
	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private IWifiStickPlantService wifiStickPlantService;
	@Resource
	private IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	ThreadPoolExecutor customThreadPool = ThreadPoolCustom.getCustomThreadPool();
	public static final Integer DEVICE_UPGRADING = 1;
	public static final String DEVICE_SERIAL_NUMBER = "deviceSerialNumber";
	public static final String SERIAL_NUMBER = "serialNumber";
	public static final Integer EXPIRED_TIME = 10;
	public static final String OFFLINE = "0";
	public static final String BIG_TYPE = "bigType";
	public static final String SMALL_TYPE = "smallType";

	@Override
	public JSONObject queryUpgradeEnable(Map<String, Object> deviceSoftwareVersionInfo, Query query) {
		JSONObject jsonObject = new JSONObject();
		try {
			Boolean upgradeEnable = false;
			// 移花接木
			deviceSoftwareVersionInfo.put(SERIAL_NUMBER, deviceSoftwareVersionInfo.get(DEVICE_SERIAL_NUMBER));
			deviceSoftwareVersionInfo.remove(DEVICE_SERIAL_NUMBER);
			deviceSoftwareVersionInfo.remove("plantId");
			// 调用web接口，查询是否可升级
			List<DeviceSoftwareVersionInfoVO> deviceSoftwareVersionInfoVOList = deviceSoftwareVersionInfoService.queryList(deviceSoftwareVersionInfo, query).getData().getRecords();

			jsonObject.put(DEVICE_SERIAL_NUMBER, deviceSoftwareVersionInfo.get(SERIAL_NUMBER));
			jsonObject.put("currentVersionNumber", "");
			// 状态0:下发升级包,1:升级中,2:升级成功,3:升级失败
			jsonObject.put("currentStatus", 2);
			if (CollectionUtil.isNotEmpty(deviceSoftwareVersionInfoVOList)) {
				DeviceExitFactoryInfoEntity deviceExitFactoryInfo = new DeviceExitFactoryInfoEntity();
				deviceExitFactoryInfo.setDeviceSerialNumber((String)deviceSoftwareVersionInfo.get(SERIAL_NUMBER));
				//根据sn查询出厂信息
				DeviceExitFactoryInfoEntity modelAndProtocolBySn = deviceExitFactoryInfoService.getModelAndProtocolBySn(deviceExitFactoryInfo);
				DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfoVO = deviceSoftwareVersionInfoVOList.get(0);
				jsonObject.put("currentVersionNumber", deviceSoftwareVersionInfoVO.getCurrentVersionNumber());
				upgradeEnable = deviceSoftwareVersionInfoVO.getCanCheck();
				Integer status = deviceSoftwareVersionInfoVO.getStatus();

				OtaUpdatePackEntity otaUpdatePackEntity = new OtaUpdatePackEntity();
				otaUpdatePackEntity.setBigType((String) deviceSoftwareVersionInfo.get(BIG_TYPE));
				otaUpdatePackEntity.setSmallType((String) deviceSoftwareVersionInfo.get(SMALL_TYPE));
				otaUpdatePackEntity.setDeviceType(modelAndProtocolBySn.getDeviceType());
				// 查询升级包最新版本号
				otaUpdatePackEntity.setIsNewVersion(1);
				otaUpdatePackEntity.setFirmwareBatch(modelAndProtocolBySn.getFirmwareBatch());
				List<OtaUpdatePackEntity> otaUpdatePackEntityList = otaUpdatePackService.list(Condition.getQueryWrapper(otaUpdatePackEntity)
					.orderByDesc("update_time"));
				if (CollectionUtil.isNotEmpty(otaUpdatePackEntityList)){
					OtaUpdatePackEntity one;
					// 首先尝试找到Company字段等于"lx"的OtaUpdatePackEntity
					Optional<OtaUpdatePackEntity> lxEntity = otaUpdatePackEntityList.stream()
						.filter(entity -> "lx".equals(entity.getCompany()))
						.findFirst();
					// 找不到，则直接使用其他公司查询条件的wifi棒升级包
					one = lxEntity.orElseGet(() -> otaUpdatePackEntityList.get(0));

					// 1、能升级时（new version detected），接口返回最新版本，app进行展示。
					// 5、已经是最新版本（already the lastest version），接口不返回最新版本。
					String latestVersionNumber = one.getVersionNumber();
					// 2、升级中时（updating），接口返回最新版本，app进行展示。
					if (status.equals(1)){
						latestVersionNumber = one.getVersionNumber();
					}
					// 3、升级成功时（update completed）且不能升级时，接口不返回最新版本。
					// 4、升级失败（update failed）且不能继续升级时，接口不返回最新版本。
					if (Boolean.FALSE.equals(upgradeEnable) && (status.equals(2) || status.equals(3))){
						latestVersionNumber = "";
					}
					jsonObject.put("latestVersionNumber", latestVersionNumber);
				}
				jsonObject.put("currentStatus", status);
				// 如果缓存中有key则证明还在升级中，
				Boolean exists = bladeRedis.exists("ota:app:issue:upgrading:" + deviceSoftwareVersionInfo.get(SERIAL_NUMBER));
				if (Boolean.TRUE.equals(exists)) {
					jsonObject.put("currentStatus", DEVICE_UPGRADING);
				}

				// 预留字段，升级包描述
				jsonObject.put("otaPackDesc", "");
			}
			jsonObject.put("upgradeEnable", upgradeEnable);
			return jsonObject;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new BusinessException("client.system.error");
		}
	}

	@Override
	public R issueUpgrade(JSONObject jsonObject) {
		String bigType = (String) jsonObject.get(BIG_TYPE);
		String smallType = (String) jsonObject.get(SMALL_TYPE);
		Long plantId = jsonObject.getLong("plantId");
		String deviceSerialNumber = (String) jsonObject.get(DEVICE_SERIAL_NUMBER);
		String redisKey = "ota:app:issue:upgrading:" + deviceSerialNumber;

		Boolean exists = bladeRedis.exists(redisKey);
		if (Boolean.TRUE.equals(exists)) {
			R<String> r = new R<>();
			String currentLanguage = CommonUtil.getCurrentLanguage();
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100118.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100118.autoGetMessage(currentLanguage));
			return r;
		}

		// 判断设备状态
		R<String> r = this.determineDeviceStatus(plantId, deviceSerialNumber);
		if (r != null) {
			return r;
		}

		if (ObjectUtil.isNotNull(bigType) && ObjectUtil.isNotNull(smallType) && ObjectUtil.isNotNull(deviceSerialNumber)) {
			DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfoVO = new DeviceSoftwareVersionInfoVO();
			deviceSoftwareVersionInfoVO.setSerialNumber(deviceSerialNumber);
			deviceSoftwareVersionInfoVO.setBigType(bigType);
			deviceSoftwareVersionInfoVO.setSmallType(smallType);
			DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfoEntity = deviceSoftwareVersionInfoService.getOne(Condition.getQueryWrapper(deviceSoftwareVersionInfoVO));

			if (ObjectUtil.isNotNull(deviceSoftwareVersionInfoEntity)) {
				List<Long> deviceSoftwareVersionIds = new ArrayList<>();
				deviceSoftwareVersionIds.add(deviceSoftwareVersionInfoEntity.getId());
				//调用接口进行升级
				deviceSoftwareVersionInfoService.retry(deviceSoftwareVersionIds);
				//超时时间10分钟
				bladeRedis.setEx(redisKey, "upgrading", Duration.ofMinutes(EXPIRED_TIME));
				//持续监控key状态，超时则报给app
				monitorOtaResult(jsonObject, redisKey);
			}

			return R.status(true);
		}
		return R.fail("issue ota upgrade fail!");
	}


	public void monitorOtaResult(JSONObject jsonObject, String redisKey) {
		String bigType = (String) jsonObject.get(BIG_TYPE);
		String smallType = (String) jsonObject.get(SMALL_TYPE);
		String deviceSerialNumber = (String) jsonObject.get(DEVICE_SERIAL_NUMBER);

		Runnable otaRunnable = () -> {
			while (true) {
				log.warn("monitor ota process。。。。。"+ redisKey);
				Boolean exists = bladeRedis.exists(redisKey);
				if (Boolean.TRUE.equals(exists)) {
					try {
						Thread.sleep(20000L);
					} catch (InterruptedException e) {
						Thread.currentThread().interrupt();
						log.error("monitorOtaResult error! -> " + e.getMessage());
					}
				} else {
					// 如果key没了，但是数据库表字段还是升级中状态，则通知app超时，并更新升级状态为失败
					DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfoVO = new DeviceSoftwareVersionInfoVO();
					deviceSoftwareVersionInfoVO.setSerialNumber(deviceSerialNumber);
					deviceSoftwareVersionInfoVO.setBigType(bigType);
					deviceSoftwareVersionInfoVO.setSmallType(smallType);
					DeviceSoftwareVersionInfoEntity deviceSoftwareVersionInfo = deviceSoftwareVersionInfoService.getOne(Condition.getQueryWrapper(deviceSoftwareVersionInfoVO));
					if (deviceSoftwareVersionInfo.getStatus().equals(DEVICE_UPGRADING)) {
						Map<String, Object> objectObjectHashMap = new HashMap<>();
						objectObjectHashMap.put("deviceSn", deviceSerialNumber);
						objectObjectHashMap.put("code", I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100115.getCode());
						objectObjectHashMap.put("message", I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100115.getMessage());
						objectObjectHashMap.put("checkExpired", "");
						deviceSoftwareVersionInfoService.publishOtaUpgradeResultToApp(objectObjectHashMap);

						// 更新软件版本表status字段为更新失败
						deviceSoftwareVersionInfo.setStatus(3);
						deviceSoftwareVersionInfoService.updateById(deviceSoftwareVersionInfo);
					}
					break;
				}
			}
		};

		// Submit the task to the thread pool for execution
		customThreadPool.submit(otaRunnable);
	}



	public R<String> determineDeviceStatus(Long plantId, String deviceSerialNumber) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		R<String> r = new R<>();
		WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
		wifiStickPlant.setPlantId(plantId);
		wifiStickPlant.setDeviceSerialNumber(deviceSerialNumber);
		WifiStickPlantEntity wifiStickPlantInfo = wifiStickPlantService.getOne(Condition.getQueryWrapper(wifiStickPlant));
		if (ObjectUtil.isNotNull(wifiStickPlantInfo)) {
			if (OFFLINE.equals(wifiStickPlantInfo.getWifiStickStatus())) {
				r.setCode(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100116.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100116.autoGetMessage(currentLanguage));
				return r;
			}
		} else {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100117.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100117.autoGetMessage(currentLanguage));
			return r;
		}
		return null;
	}

}
