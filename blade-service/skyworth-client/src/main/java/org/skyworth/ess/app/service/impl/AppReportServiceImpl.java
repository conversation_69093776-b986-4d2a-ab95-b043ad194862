package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppReportDataVO;
import org.skyworth.ess.app.vo.AppReportDetailVO;
import org.skyworth.ess.app.vo.AppReportHeaderVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDeviceCurrentStatusService;
import org.skyworth.ess.device.service.IDeviceEverydayTotalService;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.FunctionSetName;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppReportServiceImpl {
	@Resource
	IBatteryCurrentStatusService batteryCurrentStatusService;
	@Resource
	private IDeviceCurrentStatusService deviceCurrentStatusService;
	@Resource
	private DeviceLog22ByDorisService deviceLog22ByDorisService;
	@Resource
	private IBatteryEverydayTotalService batteryEverydayTotalService;
	@Resource
	private IDevice23Service device23Service;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private IDeviceEverydayTotalService deviceEverydayTotalService;
	@Resource
	private TimeZoneDeviceService timeZoneDeviceService;
	@Resource
	private AppReportParallelServiceImpl appReportParallelServiceImpl;
	Function<BatteryEverydayTotalVO,BigDecimal> pvDailyEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyDischargeEnergyOrParallelFun = BatteryEverydayTotalVO::getAppBatteryDailyDischargeEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> loadAddOrParallelFun = BatteryEverydayTotalVO::getAppLoadAddEps;
	Function<BatteryEverydayTotalVO,BigDecimal> todayImportEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayImportEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> todayExportEnergyOrParallelFun = BatteryEverydayTotalVO::getAppTodayExportEnergy;
	Function<BatteryEverydayTotalVO,BigDecimal> batteryDailyChargeEnergyOrParallelFun = BatteryEverydayTotalVO::getAppBatteryDailyChargeEnergy;

	public AppReportHeaderVO queryPlantRunningStateHeaderV2(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		try {
			// 并机查询并机数据
//			if(Constants.ONE.equals(appVO.getIsParallelMode())) {
//				appReportParallelServiceImpl.getParallelHeaderCardData(appVO,resultAppReportHeaderVO);
//			} else {
				this.setHeadCardData(appVO, resultAppReportHeaderVO);
//			}
		} catch (Exception e) {
			log.error("error ParseException :{} ", e);
		}
		return resultAppReportHeaderVO;
	}


	private void completionMonth(AppReportDetailVO report) {
		List<AppReportDataVO> pvList = report.getPvGenerationList();
		// 只用初始化一次， 只是在 getNotExistKeyList 做比较，不作为最终数据，最终数据是 将db中的原始数据 + 不在 initDaysMap 中的数据 addAll
		Map<String, BigDecimal> initDaysMap = this.initMonthsMap();
		List<AppReportDataVO> resultList = this.getNotExistKeyList(initDaysMap, pvList);
		pvList.addAll(resultList);

		List<AppReportDataVO> batteryList = report.getBatteryOutputList();
		batteryList.addAll(this.getNotExistKeyList(initDaysMap, batteryList));

		List<AppReportDataVO> powerList = report.getPowerConsumptionList();
		powerList.addAll(this.getNotExistKeyList(initDaysMap, powerList));

		List<AppReportDataVO> gridConsumptionList = report.getGridConsumptionList();
		gridConsumptionList.addAll(this.getNotExistKeyList(initDaysMap, gridConsumptionList));

		List<AppReportDataVO> feedInGridList = report.getFeedInGridList();
		feedInGridList.addAll(this.getNotExistKeyList(initDaysMap, feedInGridList));

		List<AppReportDataVO> batteryInputList = report.getBatteryInputList();
		batteryInputList.addAll(this.getNotExistKeyList(initDaysMap, batteryInputList));

		List<AppReportDataVO> otherPvList = report.getOtherPvList();
		otherPvList.addAll(this.getNotExistKeyList(initDaysMap, otherPvList));
		this.sortList(pvList);
		this.sortList(batteryList);
		this.sortList(powerList);

		this.sortList(gridConsumptionList);
		this.sortList(feedInGridList);
		this.sortList(batteryInputList);
		this.sortList(otherPvList);
		// 将前一个总量减去当前总量，为当前累计的
//		this.subtractBeforeData(pvList);
//		this.subtractBeforeData(batteryList);
//		this.subtractBeforeData(powerList);

	}

	private Map<String, BigDecimal> initMonthsMap() {
		Map<String, BigDecimal> initMonthsMap = new HashMap<>();
		BigDecimal zero = new BigDecimal("0.00");
		initMonthsMap.put("01", zero);
		initMonthsMap.put("02", zero);
		initMonthsMap.put("03", zero);
		initMonthsMap.put("04", zero);
		initMonthsMap.put("05", zero);
		initMonthsMap.put("06", zero);
		initMonthsMap.put("07", zero);
		initMonthsMap.put("08", zero);
		initMonthsMap.put("09", zero);
		initMonthsMap.put("10", zero);
		initMonthsMap.put("11", zero);
		initMonthsMap.put("12", zero);
		return initMonthsMap;
	}

	private void sortList(List<AppReportDataVO> list) {
		if (list != null) {
			list.sort(Comparator.comparing(AppReportDataVO::getKey));
		}
	}

	// 补齐数据库中不存在的天数
	private void completionDay(AppReportDetailVO report, LocalDateTime beginLocalDateTime,
							   LocalDateTime endLocalDateTime,
							   String format) throws ParseException {
		List<AppReportDataVO> pvList = report.getPvGenerationList();
		LocalDate beginLocalDate = beginLocalDateTime.toLocalDate();
		LocalDate endLocalDate = endLocalDateTime.toLocalDate();
		int interval;
		if ("dd".equals(format)) {
			interval = beginLocalDate.lengthOfMonth();
		} else {
			long epochDay = endLocalDate.toEpochDay() - beginLocalDate.toEpochDay();
			interval = Long.valueOf(epochDay).intValue();
		}
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
		// 只需构造一次， 在 setDbToInitMap 中 会和db中 的 日期做比较， 由于 5个报表的日期都会是一样的， 因此如果db中的 日期和
		// 初始化 initDaysMap 中的的日期 相等，那么 initDaysMap 后面的 value会覆盖前面的 value，在根据 initDaysMap 中的所有制构造的是一个新的 list
		Map<String, BigDecimal> initDaysMap = this.initDaysMap(dateTimeFormatter, interval, beginLocalDate);
		report.setPvGenerationList(this.setDbToInitMap(initDaysMap, pvList));
//		List<AppReportDataVO> resultList = this.getNotExistKeyList(initDaysMap, pvList);
//		pvList.addAll(resultList);
		List<AppReportDataVO> batteryList = report.getBatteryOutputList();
		report.setBatteryOutputList(this.setDbToInitMap(initDaysMap, batteryList));
//		batteryList.addAll(this.getNotExistKeyList(initDaysMap, batteryList));
		List<AppReportDataVO> powerList = report.getPowerConsumptionList();
		report.setPowerConsumptionList(this.setDbToInitMap(initDaysMap, powerList));
		List<AppReportDataVO> gridConsumptionList = report.getGridConsumptionList();
		report.setGridConsumptionList(this.setDbToInitMap(initDaysMap, gridConsumptionList));

		List<AppReportDataVO> feedInGridList = report.getFeedInGridList();
		report.setFeedInGridList(this.setDbToInitMap(initDaysMap, feedInGridList));

		List<AppReportDataVO> batteryInputList = report.getBatteryInputList();
		report.setBatteryInputList(this.setDbToInitMap(initDaysMap, batteryInputList));

		List<AppReportDataVO> otherPvList = report.getOtherPvList();
		report.setOtherPvList(this.setDbToInitMap(initDaysMap, otherPvList));
//		powerList.addAll(this.getNotExistKeyList(initDaysMap, powerList));
//		this.sortList(pvList);
//		this.sortList(batteryList);
//		this.sortList(powerList);
		// 将前一个总量减去当前总量，为当前累计的
//		this.subtractBeforeData(pvList);
//		this.subtractBeforeData(batteryList);
//		this.subtractBeforeData(powerList);

	}

	private List<AppReportDataVO> setDbToInitMap(Map<String, BigDecimal> initDaysMap,
												 List<AppReportDataVO> dischargeCapacityList) {
		List<AppReportDataVO> resultList = new ArrayList<>();
		log.info("dbData ");
		for (AppReportDataVO vo : dischargeCapacityList) {
			for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
				String key = entry.getKey();
				if (key.equals(vo.getKey())) {
					entry.setValue(vo.getValue());
					break;
				}
			}
		}
		for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
			String key = entry.getKey();
			BigDecimal value = entry.getValue();
			AppReportDataVO addVo = new AppReportDataVO();
			addVo.setKey(key);
			addVo.setValue(value);
			resultList.add(addVo);
		}

		return resultList;
	}

	// 初始化周、月报表天数 12.1:0  12.2:0 12.3:0 格式
	private Map<String, BigDecimal> initDaysMap(DateTimeFormatter dateTimeFormatter, int intervalDay,
												LocalDate beginLocalDate) {
		Map<String, BigDecimal> initDaysMap = new LinkedHashMap<>();
		BigDecimal zero = new BigDecimal("0.00");
		for (int i = 0; i <= intervalDay; i++) {
			LocalDate localDate = beginLocalDate.plusDays(Integer.toUnsignedLong(i));
			String formatDay = dateTimeFormatter.format(localDate);
			initDaysMap.put(formatDay, zero);
		}
//		log.info("initDaysMap : {}", initDaysMap);
		return initDaysMap;
	}

	// 将初始化数据和数据库中比较， 如果数据库中不存在此天数据，则构造一个 0
	private List<AppReportDataVO> getNotExistKeyList(Map<String, BigDecimal> initDaysMap,
													 List<AppReportDataVO> dbList) {
		List<AppReportDataVO> resultList = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
			String key = entry.getKey();
			BigDecimal value = entry.getValue();
			boolean existKey = false;
			for (AppReportDataVO vo : dbList) {
				if (key.equals(vo.getKey())) {
					existKey = true;
					break;
				}
			}
			if (!existKey) {
				AppReportDataVO addVo = new AppReportDataVO();
				addVo.setKey(key);
				addVo.setValue(value);
				resultList.add(addVo);
			}
		}
		return resultList;
	}

	// 将数据库中值转化为 key value
	private AppReportDetailVO getAppReportDetailVO(AppVO appVO, LocalDateTime beginLocalDate,
												   LocalDateTime endLocalDate,
												   SimpleDateFormat simpleDateFormat,
												   Function<QueryCondition, List<BatteryEverydayTotalVO>> fun
	) {
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setStartDateTime(DateUtil.convertLocalDateTime2Date(beginLocalDate));
		queryCondition.setEndDateTime(DateUtil.convertLocalDateTime2Date(endLocalDate));
		queryCondition.setPlantId(appVO.getPlantId());
		queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		queryCondition.setTimeZone(appVO.getTimeZone());
		List<BatteryEverydayTotalVO> batteryEverydayTotalList = fun.apply(queryCondition);
		log.info("db report type : {} ", appVO.getType());
		AppReportDetailVO dailyReport = new AppReportDetailVO();
		List<AppReportDataVO> pvGenerationList = new ArrayList<>();
		List<AppReportDataVO> batteryOutputList = new ArrayList<>();
		List<AppReportDataVO> powerConsumptionList = new ArrayList<>();
		List<AppReportDataVO> gridConsumptionList = new ArrayList<>();
		List<AppReportDataVO> feedInGridList = new ArrayList<>();
		List<AppReportDataVO> batteryInputList = new ArrayList<>();
		List<AppReportDataVO> otherPvList = new ArrayList<>();
		for (BatteryEverydayTotalVO entity : batteryEverydayTotalList) {
			AppReportDataVO pv = new AppReportDataVO();
			AppReportDataVO batteryOutput = new AppReportDataVO();
			AppReportDataVO power = new AppReportDataVO();
			AppReportDataVO gridConsumption = new AppReportDataVO();
			AppReportDataVO feedInGrid = new AppReportDataVO();
			AppReportDataVO batteryInput = new AppReportDataVO();
			AppReportDataVO otherPv = new AppReportDataVO();
			// 周报表
			if (1 == appVO.getType()) {
				String subTotalDate = entity.getAppTotalDate();
				log.info("week report db date : {}",subTotalDate);
				this.setKeyValue(subTotalDate, pv::setKey);
				this.setKeyValue(subTotalDate, batteryOutput::setKey);
				this.setKeyValue(subTotalDate, power::setKey);
				this.setKeyValue(subTotalDate, gridConsumption::setKey);
				this.setKeyValue(subTotalDate, feedInGrid::setKey);
				this.setKeyValue(subTotalDate, batteryInput::setKey);
				this.setKeyValue(subTotalDate, otherPv::setKey);
			} else if (2 == appVO.getType() || 3 == appVO.getType()) {
				// 月、年报表
				String appTotalDate = entity.getAppTotalDate();
				this.setKeyValue(appTotalDate, pv::setKey);
				this.setKeyValue(appTotalDate, batteryOutput::setKey);
				this.setKeyValue(appTotalDate, power::setKey);
				this.setKeyValue(appTotalDate, gridConsumption::setKey);
				this.setKeyValue(appTotalDate, feedInGrid::setKey);
				this.setKeyValue(appTotalDate, batteryInput::setKey);
				this.setKeyValue(appTotalDate, otherPv::setKey);
			} else {
				pv.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
				batteryOutput.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
				power.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
			}
			pv.setValue(this.divideThousand(entity.getAppTodayEnergy()));
			pvGenerationList.add(pv);
			batteryOutput.setValue(this.divideThousand(entity.getAppBatteryDailyDischargeEnergy()));
			batteryOutputList.add(batteryOutput);
			power.setValue(this.divideThousand(entity.getAppLoadAddEps()));
			powerConsumptionList.add(power);
			gridConsumption.setValue(this.divideThousand(entity.getAppTodayImportEnergy()));
			gridConsumptionList.add(gridConsumption);
			feedInGrid.setValue(this.divideThousand(entity.getAppTodayExportEnergy()));
			feedInGridList.add(feedInGrid);
			batteryInput.setValue(this.divideThousand(entity.getAppBatteryDailyChargeEnergy()));
			batteryInputList.add(batteryInput);
			otherPv.setValue(this.divideThousand(entity.getAppOtherPv()));
			otherPvList.add(otherPv);
		}
		dailyReport.setPvGenerationList(pvGenerationList);
		dailyReport.setBatteryOutputList(batteryOutputList);
		dailyReport.setPowerConsumptionList(powerConsumptionList);
		dailyReport.setGridConsumptionList(gridConsumptionList);
		dailyReport.setFeedInGridList(feedInGridList);
		dailyReport.setBatteryInputList(batteryInputList);
		dailyReport.setOtherPvList(otherPvList);
		return dailyReport;
	}

	private void setKeyValue(String value, FunctionSetName<String> function) {
		function.setName(value);
	}

	private BigDecimal divideThousand(BigDecimal value) {
		if (value == null) {
			return new BigDecimal(0);
		}
		return value.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP);
	}

	private AppReportDetailVO getHoursReportV2(AppVO appVO) throws ParseException {
		// 天报表，每5分钟累计统计
		QueryDeviceLog22Condition query = this.getQueryDeviceLog22Condition(appVO);
		// 查询结果按升序排列，同一时间段，后面的覆盖前面的
		log.info("queryPlantRunningStateV2 getHoursReportV2  query log22 begin");
		List<DeviceLog22VO> deviceLog22VoList = deviceLog22ByDorisService.appReportEstimateV2(query);
		log.info("queryPlantRunningStateV2 getHoursReportV2  query log22 end");
		// 服务器时间转换为设备时间:addTime2025-03-12
		covertToDeviceTimeStamp(deviceLog22VoList, appVO.getTimeZone());
		// mysql 测试数据
//		List<DeviceLog22VO> deviceLog22VoList = batteryEverydayTotalService.appReportEstimate(query);
		// 初始化每5分钟数据
		Map<String, BigDecimal> pvTotalInputPowerMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> batteryOutputMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> powerConsumption4LoadAddEpsMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> gridConsumptionMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> feedInGridMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> batteryInputMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> batterySocMap = this.initHoursMap(appVO);
		Map<String, BigDecimal> otherPvMap = this.initHoursMap(appVO);
		for (DeviceLog22VO deviceLog22VO : deviceLog22VoList) {
			String appTotalDate = deviceLog22VO.getAppTotalDate();
			BigDecimal appPvTotalInputPower = deviceLog22VO.getAppPvTotalInputPower();
			BigDecimal appBatteryPower = deviceLog22VO.getAppBatteryPower() == null ? BigDecimal.ZERO :
				deviceLog22VO.getAppBatteryPower();
			BigDecimal loadAddEps = deviceLog22VO.getAppLoadAddEps();
			BigDecimal appPhaserWattOfGrid = deviceLog22VO.getAppPhaserWattOfGrid() == null ? BigDecimal.ZERO :
				deviceLog22VO.getAppPhaserWattOfGrid();
			BigDecimal appPhasesWattOfGrid = deviceLog22VO.getAppPhasesWattOfGrid() == null ? BigDecimal.ZERO :
				deviceLog22VO.getAppPhasesWattOfGrid();
			BigDecimal appPhasetWattOfGrid = deviceLog22VO.getAppPhasetWattOfGrid() == null ? BigDecimal.ZERO :
				deviceLog22VO.getAppPhasetWattOfGrid();
			BigDecimal appBatterySoc = deviceLog22VO.getAppBatterySoc();

			BigDecimal appBatteryOutput = BigDecimal.ZERO;
			BigDecimal appBatteryInput = BigDecimal.ZERO;
			if (appBatteryPower.compareTo(BigDecimal.ZERO) > 0) {
				// 少于0的时候记录为0,大于0为放电
				appBatteryOutput = appBatteryPower;
			} else if (appBatteryPower.compareTo(BigDecimal.ZERO) < 0) {
				// 正数和0记录为0，负数则取绝对值，小于0则为充电
				appBatteryInput = appBatteryPower.abs();
			}

			BigDecimal addWattOfGrid = appPhaserWattOfGrid.add(appPhasesWattOfGrid).add(appPhasetWattOfGrid);
			BigDecimal gridConsumption = BigDecimal.ZERO;
			BigDecimal feedInGrid = BigDecimal.ZERO;
			// 三者相加为正则 记录为 grid consumption ，为负则记录 feed in grid ，并取绝对值
			if(addWattOfGrid.compareTo(BigDecimal.ZERO) > 0) {
				gridConsumption = addWattOfGrid;
			} else {
				feedInGrid = addWattOfGrid.abs();
			}

			int count = 0;
			String startDateKey = null;
			// 先取 第一个，然后将 第一个 和 第二个作为一个区间， 如果数据不在此区间，将 第二个作为第一个，开始下一个区间
			// 如果相等，则赋值 到 value上，跳出循环
			// 00:00  00:05 -> 00:05  00:10。   00:00 到 00:05之间，数据归集到 00:00
			for (Map.Entry<String, BigDecimal> entry : pvTotalInputPowerMap.entrySet()) {
				if (count == 0) {
					startDateKey = entry.getKey();
					count++;
					continue;
				}
				String endDateKey = entry.getKey();
				if (appTotalDate.compareTo(startDateKey) >= 0 && appTotalDate.compareTo(endDateKey) < 0) {
					this.setMapValue(pvTotalInputPowerMap, startDateKey, appPvTotalInputPower);
					this.setMapValue(batteryOutputMap, startDateKey, appBatteryOutput);
					this.setMapValue(powerConsumption4LoadAddEpsMap, startDateKey, loadAddEps);
					this.setMapValue(gridConsumptionMap, startDateKey, gridConsumption);
					this.setMapValue(feedInGridMap, startDateKey, feedInGrid);
					this.setMapValue(batteryInputMap, startDateKey, appBatteryInput);
					this.setMapValue(batterySocMap, startDateKey, appBatterySoc);
					this.setMapValue(otherPvMap, startDateKey, deviceLog22VO.getAppOtherPv());
					break;
				} else {
					startDateKey = endDateKey;
				}
			}

		}
		AppReportDetailVO appHoursReportDetailVO = this.getAppHoursReportDetailVO(pvTotalInputPowerMap,
			batteryOutputMap, powerConsumption4LoadAddEpsMap);
		this.setOtherList(appHoursReportDetailVO, gridConsumptionMap, feedInGridMap, batteryInputMap, batterySocMap,otherPvMap);
		return appHoursReportDetailVO;

	}

	/**
	 * 服务器时间转换为设备时间
	 *
	 * @param deviceLog22VoList 数据集合
	 * @param timeZone          入参
	 * <AUTHOR>
	 * @date 2025/3/12 10:15
	 */
	private void covertToDeviceTimeStamp(List<DeviceLog22VO> deviceLog22VoList, String timeZone) {
		Optional.ofNullable(deviceLog22VoList).orElse(new ArrayList<>()).forEach(a -> {
			Date appDeviceDateTime = DateUtil.convertDateTimeZone(a.getAppDeviceDateTime(),
				CommonConstant.COMMON_DEFAULT_TIME_ZONE, timeZone);
			a.setAppDeviceDateTime(appDeviceDateTime);
			a.setAppTotalDate(DateUtil.parseDateToString(appDeviceDateTime, DateUtil.PATTERN_HOURS_MINUTE));
		});
	}


	private QueryDeviceLog22Condition getQueryDeviceLog22Condition(AppVO appVO) {
		String timeZone = appVO.getTimeZone();
		String dateScope = appVO.getDataScope();
		if (StringUtil.isAnyBlank(timeZone, dateScope)) {
			throw new BusinessException("client.parameter.error.empty");
		}
		try {
			LocalDateTime begin = DateUtil.convertStringTimeZone(dateScope + CommonConstant.COMMON_DAY_START_TIME,
				timeZone, CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			LocalDateTime end = DateUtil.convertStringTimeZone(dateScope + CommonConstant.COMMON_DAY_END_TIME,
				timeZone, CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			QueryDeviceLog22Condition query = new QueryDeviceLog22Condition();
			query.setStartDateTime(DateUtil.convertLocalDateTime2Date(begin));
			query.setEndDateTime(DateUtil.convertLocalDateTime2Date(end));
			query.setPlantId(appVO.getPlantId());
			query.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
			return query;
		} catch (Exception e) {
			log.error("getQueryDeviceLog22Condition error", e);
			throw new BusinessException("client.parameter.error.invalidDateFormat");
		}
	}


	private void setOtherList(AppReportDetailVO appReportDetailVO, Map<String, BigDecimal> gridConsumptionMap,
							  Map<String, BigDecimal> feedInGridMap,
							  Map<String, BigDecimal> batteryInputMap, Map<String, BigDecimal> batterySocMap,
							  Map<String, BigDecimal> otherPvMap) {
		appReportDetailVO.setGridConsumptionList(this.addData(gridConsumptionMap));
		appReportDetailVO.setFeedInGridList(this.addData(feedInGridMap));
		appReportDetailVO.setBatteryInputList(this.addData(batteryInputMap));
		appReportDetailVO.setBatterySocList(this.addData(batterySocMap));
		appReportDetailVO.setOtherPvList(this.addData(otherPvMap));
		log.info("queryPlantRunningStateV2 getHoursReportV2 setOtherList end");
	}

	private List<AppReportDataVO> addData(Map<String, BigDecimal> map) {
		List<AppReportDataVO> addList = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
			AppReportDataVO pv = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
			pv.setKey(entry.getKey());
			pv.setValue(entry.getValue());
			addList.add(pv);
		}
		return addList;
	}

	private AppReportDetailVO getAppHoursReportDetailVO(Map<String, BigDecimal> pvTotalInputPowerMap, Map<String,
		BigDecimal> batteryOutputMap,
														Map<String, BigDecimal> loadAddEpsMap) {
		AppReportDetailVO hourReport = new AppReportDetailVO();
		List<AppReportDataVO> pvList = new ArrayList<>();
		List<AppReportDataVO> batteryOutputList = new ArrayList<>();
		List<AppReportDataVO> loadAddEpsList = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : pvTotalInputPowerMap.entrySet()) {
			AppReportDataVO pv = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
			pv.setKey(entry.getKey());
			pv.setValue(entry.getValue());
			pvList.add(pv);
		}
		for (Map.Entry<String, BigDecimal> entry : batteryOutputMap.entrySet()) {
			AppReportDataVO battery = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
			battery.setKey(entry.getKey());
			battery.setValue(entry.getValue());
			batteryOutputList.add(battery);
		}
		for (Map.Entry<String, BigDecimal> entry : loadAddEpsMap.entrySet()) {
			// 总充电量
			AppReportDataVO loadAddEps = new AppReportDataVO();
			if("24:00".equals(entry.getKey())) {
				continue;
			}
			loadAddEps.setKey(entry.getKey());
			loadAddEps.setValue(entry.getValue());
			loadAddEpsList.add(loadAddEps);
		}

		hourReport.setPvGenerationList(pvList);
		hourReport.setBatteryOutputList(batteryOutputList);
		hourReport.setPowerConsumptionList(loadAddEpsList);
		log.info("queryPlantRunningStateV2 getHoursReportV2  end");
		return hourReport;
	}

	private void setMapValue(Map<String, BigDecimal> map, String key, BigDecimal value) {
		if (value == null) {
			return;
		}
		map.put(key, value.setScale(1, RoundingMode.HALF_UP));
	}

	private void subtractBeforeData(List<AppReportDataVO> list) {
		BigDecimal beforeCharge = new BigDecimal(0);
		for (AppReportDataVO vo : list) {
			BigDecimal value = vo.getValue();
			if (value.doubleValue() != 0) {
				if (beforeCharge.compareTo(value) > 0) {
					continue;
				}
				BigDecimal result = value.subtract(beforeCharge);
				vo.setValue(result);
				beforeCharge = value;
			}
		}
	}

	private Map<String, BigDecimal> initHoursMap(AppVO appVO) {
		// 初始化起始点为 00:00
		LocalDateTime localDateTime = LocalDateTime.of(2023, 1, 1, 0, 0);
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
		LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
		// 计算一天有 288 个点
		for (int i = 0; i < 288; i++) {
			String format = dateTimeFormatter.format(localDateTime);
			// 兼容老版本APP接口
			if ("v3".equals(appVO.getInterfaceVersion())) {
				map.put(format, null);//new BigDecimal("0.0")
			} else {
				map.put(format, new BigDecimal("0.0"));
			}
			// 往后推5分钟
			localDateTime = localDateTime.plusSeconds(300);
		}
		if ("v3".equals(appVO.getInterfaceVersion())) {
			map.put("24:00", null);
		} else {
			map.put("24:00", new BigDecimal("0.0"));
		}
		return map;
	}


	private AppReportHeaderVO setReportHeaderData(AppVO appVO) throws ParseException {
		AppReportHeaderVO appReportHeaderVO = new AppReportHeaderVO();
		setHeadCardData(appVO, appReportHeaderVO);
		setChartCardData(appVO, appReportHeaderVO);
		return appReportHeaderVO;
	}


	private void setHeadCardData(AppVO appVO, AppReportHeaderVO appReportHeaderVO) {
		// 查询报表头部数据，注意不要调换顺序
		this.getBatteryData(appVO, appReportHeaderVO);
		//查询电网数据
		this.getDeviceData(appVO, appReportHeaderVO);
	}

	private void setChartCardData(AppVO appVO, AppReportHeaderVO appReportHeaderVO) throws ParseException {
		// 获取pv、grid数据
		this.getFromPvAndGrid(appVO, appReportHeaderVO);
	}

	private DeviceEverydayTotalVO setDeviceData(Supplier<BigDecimal> todayEnergy,
												Supplier<BigDecimal> todayExportEnergy
		, Supplier<BigDecimal> todayLoadEnergy, Supplier<BigDecimal> dailyEnergyToEps,
												Supplier<BigDecimal> batteryDailyChargeEnergy
		, Supplier<BigDecimal> todayImportEnergy) {
		DeviceEverydayTotalVO deviceEverydayTotalVO = new DeviceEverydayTotalVO();
		deviceEverydayTotalVO.setSumTodayEnergy(todayEnergy.get());
		deviceEverydayTotalVO.setSumTodayExportEnergy(todayExportEnergy.get());
		deviceEverydayTotalVO.setSumTodayLoadEnergy(todayLoadEnergy.get());
		deviceEverydayTotalVO.setSumDailyEnergyToEps(dailyEnergyToEps.get());
		deviceEverydayTotalVO.setSumBatteryDailyChargeEnergy(batteryDailyChargeEnergy.get());
		deviceEverydayTotalVO.setSumTodayImportEnergy(todayImportEnergy.get());
		return deviceEverydayTotalVO;
	}

	private void getFromPvAndGrid(AppVO appVO, AppReportHeaderVO appReportHeaderVO) throws ParseException {
		log.info("queryPlantRunningStateV2 getFromPvAndGrid  begin");
		String dataScope = appVO.getDataScope();
		String timeZone = appVO.getTimeZone();
		// 查询日月年报表数据
		if (0 == appVO.getType()) {
			QueryDeviceLog22Condition query = this.getQueryDeviceLog22Condition(appVO);
			// 当天报表
			DeviceLog22VO deviceLog22VOS = deviceLog22ByDorisService.appDailyFromPvAndGrid(query);
			if (deviceLog22VOS == null) {
				deviceLog22VOS = new DeviceLog22VO();
			}
			DeviceEverydayTotalVO deviceEverydayTotalVO = this.setDeviceData(deviceLog22VOS::getOriginalTodayEnergy,
				deviceLog22VOS::getOriginalTodayExportEnergy
				, deviceLog22VOS::getOriginalTodayLoadEnergy, deviceLog22VOS::getOriginalDailyEnergyToEps
				, deviceLog22VOS::getOriginalBatteryDailyChargeEnergy, deviceLog22VOS::getOriginalTodayImportEnergy);
			this.setWeekMonthAnnualPieData(appReportHeaderVO, deviceEverydayTotalVO);
		} else if (1 == appVO.getType()) {
			// 获取设备所处的时间：年月日
			LocalDateTime deviceLocalDateTime =
				DateUtil.convertStringTimeZone(dataScope + CommonConstant.COMMON_DAY_START_TIME, timeZone,
					CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			LocalDateTime firstDayOfWeek = deviceLocalDateTime.minusDays(7);
			//deviceLocalDateTime.minusDays(1);
			// 周报表，过去7天
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.pieReport(
				this.getBatteryQueryCondition(appVO, firstDayOfWeek, deviceLocalDateTime));
			log.info("fromPvAndGrid week db: {} ", batteryEverydayTotalEntity);
			this.setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
		} else if (2 == appVO.getType()) {
			LocalDateTime deviceDateTime = DateUtil.stringDateTime2LocalDateTime(dataScope + CommonConstant.COMMON_DAY_START_TIME);
			YearMonth yearMonth = YearMonth.from(deviceDateTime);
			LocalDateTime firstDayOfMonth = yearMonth.atDay(1).atStartOfDay();
			LocalDateTime lastDayOfMonth = yearMonth.atEndOfMonth().atTime(LocalTime.MAX);
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.pieReport(
				getBatteryQueryCondition(appVO,
					convertLocalDateTimeZone(firstDayOfMonth, timeZone),
					convertLocalDateTimeZone(lastDayOfMonth, timeZone))
			);
			log.info("fromPvAndGrid month db: {}", batteryEverydayTotalEntity);
			setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
		} else if (3 == appVO.getType()) {
			LocalDateTime deviceDateTime = DateUtil.stringDateTime2LocalDateTime(dataScope + CommonConstant.COMMON_DAY_START_TIME);
			int year = deviceDateTime.getYear();
			LocalDateTime firstDayOfYear = LocalDateTime.of(year, 1, 1, 0, 0, 0);
			LocalDateTime lastDayOfYear = LocalDateTime.of(year, 12, 31, 23, 59, 59);
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalService.pieReport(
				getBatteryQueryCondition(appVO,
					convertLocalDateTimeZone(firstDayOfYear, timeZone),
					convertLocalDateTimeZone(lastDayOfYear, timeZone))
			);
			log.info("fromPvAndGrid year db: {}", batteryEverydayTotalEntity);
			setWeekMonthAnnualPieDataV2(appReportHeaderVO, batteryEverydayTotalEntity);
		}
		log.info("queryPlantRunningStateV2 getFromPvAndGrid  end");
	}
	private LocalDateTime convertLocalDateTimeZone(LocalDateTime localDateTime, String timeZone) {
		try {
			return DateUtil.convertLocalDateTimeZone(localDateTime, timeZone, CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		} catch (Exception e) {
			log.error("Failed to convert local date time to time zone: {}", localDateTime, e);
			return null;
		}
	}
	private void setWeekMonthAnnualPieDataV2(AppReportHeaderVO appReportHeaderVO,
											 BatteryEverydayTotalEntity batteryEverydayTotalEntity) {
		if (batteryEverydayTotalEntity == null) {
			batteryEverydayTotalEntity = new BatteryEverydayTotalEntity();
		}
		BigDecimal selfConsumed = batteryEverydayTotalEntity.getSelfConsumed() == null ? BigDecimal.ZERO :
			batteryEverydayTotalEntity.getSelfConsumed();
		BigDecimal fedToGrid = batteryEverydayTotalEntity.getFedToGrid() == null ? BigDecimal.ZERO :
			batteryEverydayTotalEntity.getFedToGrid();
		BigDecimal selfSufficiency = batteryEverydayTotalEntity.getSelfSufficiency() == null ? BigDecimal.ZERO :
			batteryEverydayTotalEntity.getSelfSufficiency();
		BigDecimal fromGrid = batteryEverydayTotalEntity.getFromGrid() == null ? BigDecimal.ZERO :
			batteryEverydayTotalEntity.getFromGrid();

		appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(selfConsumed, 1));
		appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(fedToGrid, 1));
		BigDecimal firstPieAddResult = selfConsumed.add(fedToGrid);
		if (BigDecimal.ZERO.compareTo(firstPieAddResult) == 0) {
			appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
			appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
			appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
		} else {
			BigDecimal selfConsumedRatio = selfConsumed.divide(firstPieAddResult, BizConstant.NUMBER_TWO,
					RoundingMode.HALF_UP)
				.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
			BigDecimal fedToGridRatio = new BigDecimal(100).subtract(selfConsumedRatio);
			appReportHeaderVO.setSelfConsumedRatio(selfConsumedRatio);
			appReportHeaderVO.setFedToGridRatio(fedToGridRatio);
			appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1,
				selfConsumed, fedToGrid));
		}

		appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(selfSufficiency, 1));
		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(fromGrid, 1));
		BigDecimal secondPieAddResult = selfSufficiency.add(fromGrid);
		if (BigDecimal.ZERO.compareTo(secondPieAddResult) == 0) {
			appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
			appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
			appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
		} else {
			BigDecimal selfSufficiencyRatio = selfSufficiency.divide(secondPieAddResult, BizConstant.NUMBER_TWO,
					RoundingMode.HALF_UP)
				.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
			BigDecimal fromGridRatio = new BigDecimal(100).subtract(selfSufficiencyRatio);
			appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
			appReportHeaderVO.setFromGridRatio(fromGridRatio);
			appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1,
				selfSufficiency, fromGrid));
		}

	}

	private void setWeekMonthAnnualPieData(AppReportHeaderVO appReportHeaderVO,
										   DeviceEverydayTotalVO deviceEverydayTotalVO) {
		BigDecimal sumTodayEnergy = deviceEverydayTotalVO.getSumTodayEnergy() == null ? BigDecimal.ZERO :
			deviceEverydayTotalVO.getSumTodayEnergy();
		BigDecimal sumTodayExportEnergy = deviceEverydayTotalVO.getSumTodayExportEnergy() == null ? BigDecimal.ZERO :
			deviceEverydayTotalVO.getSumTodayExportEnergy();
		BigDecimal sumTodayLoadEnergy = deviceEverydayTotalVO.getSumTodayLoadEnergy() == null ? BigDecimal.ZERO :
			deviceEverydayTotalVO.getSumTodayLoadEnergy();
		BigDecimal sumDailyEnergyToEps = deviceEverydayTotalVO.getSumDailyEnergyToEps() == null ? BigDecimal.ZERO :
			deviceEverydayTotalVO.getSumDailyEnergyToEps();
		BigDecimal sumBatteryDailyChargeEnergy = deviceEverydayTotalVO.getSumBatteryDailyChargeEnergy() == null ?
			BigDecimal.ZERO : deviceEverydayTotalVO.getSumBatteryDailyChargeEnergy();
		BigDecimal sumTodayImportEnergy = deviceEverydayTotalVO.getSumTodayImportEnergy() == null ? BigDecimal.ZERO :
			deviceEverydayTotalVO.getSumTodayImportEnergy();
		// 设置第一排饼图数据
		if (sumTodayEnergy.compareTo(sumTodayExportEnergy) >= 0) {
			BigDecimal selfConsumed = sumTodayEnergy.subtract(sumTodayExportEnergy);
			appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(selfConsumed, 1));
			appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(sumTodayExportEnergy, 1));
			// 两边的总数
			BigDecimal addResult = selfConsumed.add(sumTodayExportEnergy);
			if (BigDecimal.ZERO.compareTo(addResult) == 0) {
				appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
				appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
				appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
			} else {
				BigDecimal selfConsumedRatio = selfConsumed.divide(addResult, BizConstant.NUMBER_TWO,
					RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
				BigDecimal fedToGridRatio = new BigDecimal(100).subtract(selfConsumedRatio);
				appReportHeaderVO.setSelfConsumedRatio(selfConsumedRatio);
				appReportHeaderVO.setFedToGridRatio(fedToGridRatio);
				appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1,
					selfConsumed, sumTodayExportEnergy));
			}
		} else {
			appReportHeaderVO.setSelfConsumed(DataUnitConversionUtil.getChangeEnergyResult(sumTodayEnergy, 1));
			appReportHeaderVO.setFedToGrid(DataUnitConversionUtil.getChangeEnergyResult(BigDecimal.ZERO, 1));
			if (sumTodayEnergy.compareTo(BigDecimal.ZERO) == 0) {
				appReportHeaderVO.setSelfConsumedRatio(BigDecimal.ZERO);
			} else {
				appReportHeaderVO.setSelfConsumedRatio(new BigDecimal(100));
			}
			appReportHeaderVO.setFedToGridRatio(BigDecimal.ZERO);
			appReportHeaderVO.setSelfConsumedAndFedToGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1,
				sumTodayEnergy, BigDecimal.ZERO));
		}

		// 设置第二排饼图数据
		BigDecimal addResult = sumTodayLoadEnergy.add(sumDailyEnergyToEps).add(sumBatteryDailyChargeEnergy);
		BigDecimal selfSufficiency = addResult.subtract(sumTodayImportEnergy);
		appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(selfSufficiency, 1));
		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(sumTodayImportEnergy, 1));
		appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyAddResult(1,
			selfSufficiency, sumTodayImportEnergy));
		if (addResult.compareTo(BigDecimal.ZERO) > 0 && sumTodayImportEnergy.compareTo(BigDecimal.ZERO) > 0) {
			// 如果相减为负数，则取 1027 地址值
			if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
				log.info("addResult and sumTodayImportEnergy both more than zero, but selfSufficiency is negative");
				this.rightIsNegative(appReportHeaderVO, sumTodayEnergy, sumTodayImportEnergy);
			} else {
				// 两边的总数
				BigDecimal add = selfSufficiency.add(sumTodayImportEnergy);
				BigDecimal selfSufficiencyRatio = selfSufficiency.divide(add, BizConstant.NUMBER_TWO,
					RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
				appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
				appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
			}
		} else if (addResult.compareTo(BigDecimal.ZERO) == 0 && sumTodayImportEnergy.compareTo(BigDecimal.ZERO) == 0) {
			appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
			appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
			log.info("addResult and sumTodayImportEnergy both is zero");
		} else { // 2边上报数据有一边为负数场景 或者 为0的场景
			if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
				log.info("addResult and sumTodayImportEnergy subtract less zero");
				this.rightIsNegative(appReportHeaderVO, sumTodayEnergy, sumTodayImportEnergy);
			} else {
				BigDecimal add = selfSufficiency.add(sumTodayImportEnergy);
				if (add.compareTo(BigDecimal.ZERO) != 0) {
					log.info("addResult and sumTodayImportEnergy subtract is not zero");
					BigDecimal selfSufficiencyRatio = selfSufficiency.divide(add, BizConstant.NUMBER_TWO,
						RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
					appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
					appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
				} else {
					log.info("addResult and sumTodayImportEnergy subtract add is zero");
					appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
					appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
				}
			}
		}
	}

	private void rightIsNegative(AppReportHeaderVO appReportHeaderVO, BigDecimal sumTodayEnergy,
								 BigDecimal sumTodayImportEnergy) {
		appReportHeaderVO.setSelfSufficiency(DataUnitConversionUtil.getChangeEnergyResult(sumTodayEnergy, 1));
		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(sumTodayImportEnergy, 1));
		BigDecimal otherAdd = sumTodayEnergy.add(sumTodayImportEnergy);
		appReportHeaderVO.setSelfSufficiencyAndFromGridTotal(DataUnitConversionUtil.getChangeEnergyResult(otherAdd,
			1));
		if (otherAdd.compareTo(BigDecimal.ZERO) == 0) {
			appReportHeaderVO.setSelfSufficiencyRatio(BigDecimal.ZERO);
			appReportHeaderVO.setFromGridRatio(BigDecimal.ZERO);
		} else {
			BigDecimal selfSufficiencyRatio = sumTodayEnergy.divide(otherAdd, BizConstant.NUMBER_TWO,
				RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
			appReportHeaderVO.setSelfSufficiencyRatio(selfSufficiencyRatio);
			appReportHeaderVO.setFromGridRatio(new BigDecimal(100).subtract(selfSufficiencyRatio));
		}
	}

	private void setFromPvAndGrid(BigDecimal fromPv, BigDecimal fromGrid, AppReportHeaderVO appReportHeaderVO) {
		BigDecimal totalPvGrid = new BigDecimal("0");
		if (fromGrid == null) {
			fromGrid = new BigDecimal("0");
		}
		if (fromPv != null) {
			totalPvGrid = fromPv.add(fromGrid);
		}
		BigDecimal fromPvRatio = new BigDecimal("0");
		BigDecimal fromGridRatio = new BigDecimal("0");
		if (totalPvGrid.compareTo(new BigDecimal("0")) != 0) {
			fromPvRatio =
				fromPv.divide(totalPvGrid, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0);
			fromGridRatio = new BigDecimal(100).subtract(fromPvRatio);//fromGrid.divide(totalPvGrid, BizConstant
			// .NUMBER_TWO, RoundingMode.HALF_UP);
		}

		appReportHeaderVO.setFromPv(DataUnitConversionUtil.getChangeEnergyResult(fromPv, 1));
		appReportHeaderVO.setFromGrid(DataUnitConversionUtil.getChangeEnergyResult(fromGrid, 1));
		appReportHeaderVO.setPvGridTotal(DataUnitConversionUtil.getChangeEnergyResult(totalPvGrid, 1));
		appReportHeaderVO.setFromPvRatio(fromPvRatio);
		appReportHeaderVO.setFromGridRatio(fromGridRatio);
	}

	private QueryCondition getBatteryQueryCondition(AppVO appVO, LocalDateTime beginLocalDate,
													LocalDateTime endLocalDate) {
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setStartDateTime(DateUtil.convertLocalDateTime2Date(beginLocalDate));
		queryCondition.setEndDateTime(DateUtil.convertLocalDateTime2Date(endLocalDate));
		queryCondition.setPlantId(appVO.getPlantId());
		queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		return queryCondition;
	}


	private void getBatteryData(AppVO appVO, AppReportHeaderVO appReportHeaderV2VO) {
		BatteryCurrentStatusEntity queryBatteryCurrentStatusEntity = new BatteryCurrentStatusEntity();
		queryBatteryCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryBatteryCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		log.info("queryPlantRunningStateV2 getBatteryData query current status begin");
		List<BatteryCurrentStatusEntity> dbBatteryCurrentStatuList =
			batteryCurrentStatusService.list(Condition.getQueryWrapper(queryBatteryCurrentStatusEntity));
		log.info("queryPlantRunningStateV2 query current status end");
		if (CollectionUtil.isNotEmpty(dbBatteryCurrentStatuList)) {
			BatteryCurrentStatusEntity dbBatteryCurrentStatus = dbBatteryCurrentStatuList.get(0);
			log.info("before getBatteryData deviceSerialNumber : {}", appVO.getDeviceSerialNumber());
			// 数据库中为瓦
			BigDecimal todayEnergyTemp = dbBatteryCurrentStatus.getTodayEnergy();
			log.info("before getBatteryData todayEnergy : {}", todayEnergyTemp);
			appReportHeaderV2VO.setTodayEnergy(DataUnitConversionUtil.getChangeEnergyResult(todayEnergyTemp, 1));
			appReportHeaderV2VO.setOriginalTodayEnergy(todayEnergyTemp);// 2024.3月版本
			BigDecimal total = dbBatteryCurrentStatus.getTotalEnergy() == null ? new BigDecimal(0) :
				dbBatteryCurrentStatus.getTotalEnergy();
			BigDecimal multiplyTotal = total.multiply(BizConstant.THOUSAND);
			log.info("before getBatteryData TotalEnergy : {}", multiplyTotal);
			// 数据库中本来为千瓦，所以上面乘以 1000 。  1021,单位为kWh，显示整数，没有小数点，只能显示1kWh，10kWh
			appReportHeaderV2VO.setTotalEnergy(DataUnitConversionUtil.getChangeEnergyResult4Scale(multiplyTotal, 1,BizConstant.NUMBER_ZERO,BizConstant.NUMBER_ZERO));
			BigDecimal batteryDailyDischargeEnergy = dbBatteryCurrentStatus.getBatteryDailyDischargeEnergy();
			log.info("before getBatteryData batteryDailyDischarge : {}", batteryDailyDischargeEnergy);
			appReportHeaderV2VO.setBatteryTodayDischargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryDailyDischargeEnergy, 1));
			BigDecimal batteryTotalDischargeEnergy = dbBatteryCurrentStatus.getBatteryAccumulatedDischargeEnergy();
			log.info("before getBatteryData batteryTotalDischarge : {}", batteryTotalDischargeEnergy);
			appReportHeaderV2VO.setBatteryAccumulatedDischargeEnergy(DataUnitConversionUtil.getChangeEnergyResult(batteryTotalDischargeEnergy, 1));
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String formatStr = formatter1.format(dbBatteryCurrentStatus.getDeviceDateTime());
			log.info("app header battery db date before : {} ", formatStr);
			// AC couple 今日发电量
			BigDecimal otherPvToday = dbBatteryCurrentStatus.getEnergyTodayOfAcCoupleWh();
			appReportHeaderV2VO.setOtherPvToday(DataUnitConversionUtil.getChangeEnergyResult(otherPvToday, 1));
			// AC couple 总发电量   单位为kWh，显示整数，没有小数点，只能显示1kWh，10kWh
			BigDecimal otherPvTotal = dbBatteryCurrentStatus.getEnergyTotalOfAcCouple();
			appReportHeaderV2VO.setOtherPvTotal(DataUnitConversionUtil.extractDecimalPlaces(otherPvTotal, 0) + BizConstant.UNIT_THOUSAND_WH);
			Date deviceDateTime = null;
			try {
				deviceDateTime = formatter.parse(formatStr);
				Date now = new Date();
				log.info("app header battery db date : {} ", formatter1.format(deviceDateTime));
				log.info("app header battery now date : {} ", formatter.format(now));
				// 判断是否为今天,不为今天，则天数据为0
				if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
					appReportHeaderV2VO.setTodayEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
					appReportHeaderV2VO.setBatteryTodayDischargeEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
					appReportHeaderV2VO.setOriginalTodayEnergy(new BigDecimal(0));
					appReportHeaderV2VO.setOtherPvToday(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				}
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
		}
		log.info("queryPlantRunningStateV2 getBatteryData end");
	}

	private void getDeviceData(AppVO appVO, AppReportHeaderVO appReportHeaderV2VO) {
		DeviceCurrentStatusEntity queryDeviceCurrentStatusEntity = new DeviceCurrentStatusEntity();
		queryDeviceCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryDeviceCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		log.info("queryPlantRunningStateV2 getDeviceData  device current status begin");
		List<DeviceCurrentStatusEntity> dbDeviceCurrentStatusList =
			deviceCurrentStatusService.list(Condition.getQueryWrapper(queryDeviceCurrentStatusEntity));
		log.info("queryPlantRunningStateV2 getDeviceData  device current status end");
		if (CollectionUtil.isNotEmpty(dbDeviceCurrentStatusList)) {
			DeviceCurrentStatusEntity dbDeviceCurrentStatus = dbDeviceCurrentStatusList.get(0);
			BigDecimal todayImportEnergy = dbDeviceCurrentStatus.getTodayImportEnergy();
			BigDecimal accumulatedEnergyOfPositive = dbDeviceCurrentStatus.getAccumulatedEnergyOfPositive();
			// 当日电网输出
			BigDecimal todayExportEnergy = dbDeviceCurrentStatus.getTodayExportEnergy();
			log.info("before getDeviceImportData DeviceSerialNumber : {} ; todayImportEnergy : {} ; " +
					"totalImportEnergy" +
					" " +
					"： {} ", appVO.getDeviceSerialNumber(),
				todayImportEnergy, accumulatedEnergyOfPositive);
			appReportHeaderV2VO.setTodayImportEnergy(DataUnitConversionUtil.getChangeEnergyResult(todayImportEnergy,
				1));
			appReportHeaderV2VO.setOriginalTodayImportEnergy(todayImportEnergy);
			appReportHeaderV2VO.setTotalImportEnergy(DataUnitConversionUtil.getChangeEnergyResult(accumulatedEnergyOfPositive, 1));
//			BigDecimal fromPvBigdecimal = this.subtraction(appReportHeaderV2VO.getOriginalTodayEnergy(),
//			dbDeviceCurrentStatus.getTodayExportEnergy());
			appReportHeaderV2VO.setOriginalTodayExportEnergy(dbDeviceCurrentStatus.getTodayExportEnergy());// 2024.3月版本
			appReportHeaderV2VO.setOriginalTodayLoadEnergy(dbDeviceCurrentStatus.getTodayLoadEnergy());// 2024.3月版本
			appReportHeaderV2VO.setOriginalDailyEnergyToEps(dbDeviceCurrentStatus.getDailyEnergyToEps());// 2024.3月版本
			appReportHeaderV2VO.setOriginalBatteryDailyChargeEnergy(dbDeviceCurrentStatus.getBatteryDailyChargeEnergy());// 2024.3月版本
			appReportHeaderV2VO.setOriginalTodayImportEnergy(dbDeviceCurrentStatus.getTodayImportEnergy());// 2024.3月版本
//			appReportHeaderV2VO.setFromPv(DataUnitConversionUtil.getChangeEnergyResult(fromPvBigdecimal, 1));
//			appReportHeaderV2VO.setFromGrid(appReportHeaderV2VO.getTodayImportEnergy());
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String formatStr = formatter1.format(dbDeviceCurrentStatus.getDeviceDateTime());
			log.info("app header device db date before: {} ", formatStr);
			Date deviceDateTime = null;
			try {
				deviceDateTime = formatter1.parse(formatStr);
				Date now = new Date();
				log.info("app header device db date : {} ", formatter.format(deviceDateTime));
				log.info("app header device now date : {} ", formatter.format(now));
				// 判断是否为今天,不为今天，则天数据为0
				if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
					appReportHeaderV2VO.setTodayImportEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
//					appReportHeaderV2VO.setFromPv(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
//					appReportHeaderV2VO.setFromGrid(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				}
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			log.info("queryPlantRunningStateV2 getDeviceData  end");
		}
		LambdaQueryWrapper<Device23Entity> queryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getPlantId, appVO.getPlantId()).eq(Device23Entity::getDeviceSerialNumber, appVO.getDeviceSerialNumber());
		List<Device23Entity> device23List = device23Service.list(queryWrapper);
		if(CollectionUtil.isEmpty(device23List)){
			return;
		}
		Device23Entity device23 = device23List.get(0);
		appReportHeaderV2VO.setHybridWorkMode("");
		if (ObjUtil.isNotNull(device23) && ValidationUtil.isNotEmpty(device23.getHybridWorkMode())) {
			List<DictBiz> inverterMode =
				dictBizClient.getListByLang("inverter_mode", CommonUtil.getCurrentLanguage()).getData();
			DictBiz snj = inverterMode.stream()
				.filter(dict -> Func.equals(dict.getDictKey(), device23.getHybridWorkMode()))
				.filter(dict -> Func.equals(dict.getLanguage(), CommonUtil.getCurrentLanguage()))
				.filter(dict -> Func.equals(dict.getAttribute2(), "snj"))
				.findFirst()
				.orElse(null);

			if (ObjUtil.isNotNull(snj)) {
				appReportHeaderV2VO.setHybridWorkMode(snj.getDictValue());
			}
		}
	}

	private BigDecimal subtraction(BigDecimal subtrahend, BigDecimal minuend) {
		if (subtrahend == null) {
			return new BigDecimal(0).subtract(minuend == null ? new BigDecimal(0) : minuend);
		}
		if (minuend == null) {
			return subtrahend;
		}
		return subtrahend.subtract(minuend);
	}

	/**
	 * 查询运行状态图表数据（版本2）
	 * 该方法根据提供的应用信息查询和生成相应的运行状态报告
	 * 它支持按小时、周、月和年不同时间维度的报告生成
	 *
	 * @param appVO 包含应用相关信息的视图对象，用于生成报告
	 * @return 返回一个包含报告数据的AppReportHeaderVO对象
	 */
	public AppReportHeaderVO queryRunningStateChartV2(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		try {
			// 获取数据范围
			String dataScope = appVO.getDataScope();
			// 验证并设置时区
			String timeZone = validateAndSetTimeZone(appVO);
			// 校验 type 是否合法
			int type = appVO.getType();
			if (type < 0 || type > 3) {
				throw new IllegalArgumentException("Invalid type: " + type);
			}
			this.setChartCardData(appVO, resultAppReportHeaderVO);
			// 根据 type 执行不同的逻辑
			switch (type) {
				case 0:
					// 处理小时报告
					AppReportDetailVO hourReport = this.getHoursReportV2(appVO);
					resultAppReportHeaderVO.setDailyReport(hourReport);
					break;
				case 1:
					// 处理周报告
					// 将数据范围和时区转换为系统本地时间
					LocalDateTime deviceLocalDateTime = convertToSystemLocalDateTime(dataScope, timeZone);
					handleWeeklyReport(appVO, deviceLocalDateTime, resultAppReportHeaderVO);
					break;
				case 2:
					// 处理月报告
					handleMonthlyReport(appVO, timeZone ,resultAppReportHeaderVO);
					break;
				case 3:
					// 处理年报告
					handleAnnualReport(appVO, timeZone, resultAppReportHeaderVO);
					break;
				default:
					// 如果 type 不在预期范围内，抛出异常
					throw new IllegalStateException("Unexpected type: " + type);
			}
		} catch (IllegalArgumentException e) {
			// 处理非法参数异常
			log.warn("Invalid parameter for appVO: {}", appVO, e);
		} catch (Exception e) {
			// 处理其他异常
			log.error("Error generating report for appVO: {}", appVO, e);
		}
		// 返回结果报告对象
		return resultAppReportHeaderVO;
	}

	/**
	 * 验证并设置时区
	 *
	 * @param appVO 应用信息对象
	 * @return 有效的时区
	 */
	private String validateAndSetTimeZone(AppVO appVO) {
		String timeZone = appVO.getTimeZone();
		if (StringUtil.isBlank(timeZone)) {
			Map<String, String> timeZoneMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(
				Collections.singletonList(appVO.getPlantId()));
			timeZone = timeZoneMap.getOrDefault(appVO.getPlantId() + "", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
			appVO.setTimeZone(timeZone);
		}
		return timeZone;
	}

	/**
	 * 将设备时间转换为系统本地时间
	 *
	 * @param dataScope 数据范围
	 * @param timeZone  时区
	 * @return 系统本地时间
	 */
	private LocalDateTime convertToSystemLocalDateTime(String dataScope, String timeZone) {
		try {
			return DateUtil.convertStringTimeZone(dataScope + CommonConstant.COMMON_DAY_START_TIME, timeZone,
				CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		} catch (Exception e) {
			log.error("Failed to convert date scope to system local date time: {}", dataScope, e);
			throw new RuntimeException("Invalid date scope format", e);
		}
	}

	public static void main(String[] args) {
		AppReportServiceImpl appReportService = new AppReportServiceImpl();
		Map<String, BigDecimal> stringBigDecimalMap = appReportService.initHoursMap(new AppVO());
		LocalDateTime deviceLocalDateTime = DateUtil.convertStringTimeZone("2025-03-28" + CommonConstant.COMMON_DAY_START_TIME, "UTC+08:00",
			CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		System.out.println(deviceLocalDateTime);
		LocalDateTime firstDayOfWeekWithSystem = deviceLocalDateTime.minusDays(7);
		LocalDateTime lastDayOfWeekWithSystem = deviceLocalDateTime.minusDays(1);
		System.out.println(firstDayOfWeekWithSystem);
		System.out.println(lastDayOfWeekWithSystem);
		YearMonth yearMonthWithSystem = YearMonth.from(deviceLocalDateTime);
		// 计算系统时间下该月的第一天和最后一天
		LocalDateTime firstDayOfMonthWithSystem =
			yearMonthWithSystem.atDay(1).atTime(deviceLocalDateTime.toLocalTime());
		LocalDateTime lastDayOfMonthWithSystem =
			yearMonthWithSystem.atEndOfMonth().atTime(deviceLocalDateTime.toLocalTime());
		System.out.println(firstDayOfMonthWithSystem);
		System.out.println(lastDayOfMonthWithSystem);
	}

	/**
	 * 处理周报数据
	 * <p>
	 * 本方法旨在根据设备日期时间和应用信息，处理并生成周报数据它通过计算系统周和设备周的时间范围，
	 * 调用相应服务方法获取电池每天总计数据，并完成周报数据的详情对象构建和填充
	 *
	 * @param appVO                   App视图对象，包含应用相关信息
	 * @param deviceLocalDateTime     设备日期时间，用于计算周报的时间范围
	 * @param resultAppReportHeaderVO App报告头视图对象，用于存储生成的周报数据
	 */
	private void handleWeeklyReport(AppVO appVO, LocalDateTime deviceLocalDateTime,
									AppReportHeaderVO resultAppReportHeaderVO) {
		try {
			// 计算系统周的开始和结束时间
			LocalDateTime firstDayOfWeekWithSystem = deviceLocalDateTime.minusDays(7);
			LocalDateTime lastDayOfWeekWithSystem = deviceLocalDateTime; //deviceLocalDateTime.minusDays(1);
			// 定义获取电池每天总计数据的函数
			Function<QueryCondition, List<BatteryEverydayTotalVO>> dayFun =
				batteryEverydayTotalService::weekEstimateV2;
			// 获取系统周的日报数据
			AppReportDetailVO dailyReport = getAppReportDetailVO(appVO, firstDayOfWeekWithSystem,
				lastDayOfWeekWithSystem, new SimpleDateFormat(DateUtil.PATTERN_DATE), dayFun);
			// 将应用的数据范围转换为设备日期时间,用于初始化报表时间
			LocalDateTime deviceDateTime = DateUtil.stringDateTime2LocalDateTime(appVO.getDataScope() + CommonConstant.COMMON_DAY_START_TIME);
			// 计算设备周的开始和结束时间
			LocalDateTime firstDayOfWeekWithDevice = deviceDateTime.minusDays(7);
			LocalDateTime lastDayOfWeekWithDevice = deviceDateTime.minusDays(1);
			// 完善设备周的日报数据
			completionDay(dailyReport, firstDayOfWeekWithDevice, lastDayOfWeekWithDevice, DateUtil.PATTERN_MONTH_DAY);
			// 将周报数据填充到结果对象中
			resultAppReportHeaderVO.setWeekReport(dailyReport);
		} catch (Exception e) {
			// 日志记录周报处理异常
			log.error("Error handling weekly report for appVO: {}", appVO, e);
		}
	}

	/**
	 * 处理月度报告的相关逻辑
	 * 该方法负责根据设备日期时间和应用信息，生成月度报告，并将其设置到结果对象中
	 *
	 * @param appVO                   包含应用信息的对象，用于获取数据范围等信息
	 * @param resultAppReportHeaderVO 用于存储月度报告结果的对象
	 */
	private void handleMonthlyReport(AppVO appVO,String timeZone,
									 AppReportHeaderVO resultAppReportHeaderVO) {
		try {
			// 使用系统时间获取年月信息
			LocalDateTime deviceDateTime = DateUtil.stringDateTime2LocalDateTime(appVO.getDataScope() + CommonConstant.COMMON_DAY_START_TIME);
			YearMonth yearMonth = YearMonth.from(deviceDateTime);
			LocalDateTime firstDayOfMonth = yearMonth.atDay(1).atStartOfDay();
			LocalDateTime lastDayOfMonth = yearMonth.atEndOfMonth().atTime(LocalTime.MAX);
			// 定义获取月度电池数据的函数
			Function<QueryCondition, List<BatteryEverydayTotalVO>> monthFun =
				batteryEverydayTotalService::monthEstimateV2;
			// 获取月度报告详情对象
			AppReportDetailVO monthReport = getAppReportDetailVO(appVO,
				convertLocalDateTimeZone(firstDayOfMonth, timeZone),
				convertLocalDateTimeZone(lastDayOfMonth, timeZone)
				, new SimpleDateFormat(DateUtil.PATTERN_YEAR_MONTH), monthFun);
			// 完善月度报告详情对象的日期信息
			completionDay(monthReport,
				yearMonth.atDay(1).atTime(deviceDateTime.toLocalTime()),
				yearMonth.atEndOfMonth().atTime(deviceDateTime.toLocalTime()), DateUtil.PATTERN_DAY);
			// 将月度报告设置到结果对象中
			resultAppReportHeaderVO.setMonthlyReport(monthReport);
		} catch (Exception e) {
			// 日志记录处理月度报告时的异常
			log.error("Error handling monthly report for appVO: {}", appVO, e);
		}
	}


	/**
	 * 处理年度报告的相关逻辑
	 * 该方法负责根据应用信息和时区，生成年度报告数据，并将其设置到结果对象中
	 *
	 * @param appVO                   包含应用相关信息的对象，用于获取数据范围等信息
	 * @param timeZone                时区信息，用于将时间转换为系统默认时区
	 * @param resultAppReportHeaderVO 结果对象，用于存储生成的年度报告数据
	 */
	private void handleAnnualReport(AppVO appVO, String timeZone,
									AppReportHeaderVO resultAppReportHeaderVO) {
		try {
			// 将应用数据范围转换为LocalDateTime类型，并设定时间为年初第一天的零点
			LocalDateTime deviceDateTime =
				DateUtil.stringDateTime2LocalDateTime(appVO.getDataScope() + CommonConstant.COMMON_DAY_START_TIME);
			int year = deviceDateTime.getYear();
			LocalDateTime firstDayOfYear = LocalDateTime.of(year, 1, 1, 0, 0, 0);
			LocalDateTime lastDayOfYear = LocalDateTime.of(year, 12, 31, 23, 59, 59);
			// 定义一个函数式接口，用于年度估算
			Function<QueryCondition, List<BatteryEverydayTotalVO>> annualFun =
				batteryEverydayTotalService::annualEstimateV2;
			// 根据应用信息、时间范围和函数式接口，获取年度报告详细信息对象
			AppReportDetailVO annualReport = getAppReportDetailVO(appVO, DateUtil.convertLocalDateTimeZone(firstDayOfYear,
					timeZone,
					CommonConstant.COMMON_DEFAULT_TIME_ZONE),
				DateUtil.convertLocalDateTimeZone(lastDayOfYear,
					timeZone, CommonConstant.COMMON_DEFAULT_TIME_ZONE), new SimpleDateFormat(DateUtil.PATTERN_YEAR), annualFun);
			// 完善年度报告的月份信息
			completionMonth(annualReport);
			// 将年度报告设置到结果对象中
			resultAppReportHeaderVO.setAnnualReport(annualReport);
		} catch (Exception e) {
			// 日志记录处理年度报告时发生的错误
			log.error("Error handling annual report for appVO: {}", appVO, e);
		}
	}

}

