package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.device.service.IDeviceLog24Service;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.entity.SetItemEntity;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.tool.api.R;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 快速设置子类
 * @create-time 2023/12/1 10:45:09
 */
@Slf4j
public class AppSetupTemplateQuickStartUp extends AppSetupTemplate {
	private IDeviceLog24Service deviceLog24Service = SpringUtil.getBean(IDeviceLog24Service.class);
	private IDevice24Service device24Service = SpringUtil.getBean(IDevice24Service.class);
	private IDevice23Service device23Service = SpringUtil.getBean(IDevice23Service.class);
	private IDevice21Service device21Service = SpringUtil.getBean(IDevice21Service.class);
	private IWifiStickPlantService wifiStickPlantService = SpringUtil.getBean(IWifiStickPlantService.class);
	private ISetItemConfigService setItemConfigService = SpringUtil.getBean(ISetItemConfigService.class);
	private IDeviceIssueBiz deviceIssueBiz = SpringUtil.getBean(org.skyworth.ess.device.client.IDeviceIssueBiz.class);


	public static final String RATED_VOLTAGE = "ratedVoltage";
	public static final String RATED_FREQUENCY = "ratedFrequency";

	@Override
	public Map<String, Object> getSetupDataByDb(Long plantId, String deviceSerialNumber) {
		return deviceLog24Service.getAllQuickSetup(plantId, deviceSerialNumber);
	}

//	@Override
//	public SetItemEntity getSetupConfig(AppSetRequestVO appSetRequestVO) {
//		SetItemEntity setItemEntity = new SetItemEntity();
//		//查询设置项
//		List<SetItemConfigEntity> setItemConfig = setItemConfigService.getSetupConfig(appSetRequestVO);
//		setItemEntity.setSetItemConfigEntityList(setItemConfig);
//		return setItemEntity;
//	}

	@Override
	public void completeConfigItemBySubTemplate(Map<String, Object> setupDataByDb, SetItemEntity setupItemConfig,
												JSONObject dataObject, AppSetRequestVO appSetRequestVO) {
		if (ObjectUtil.isNotNull(setupDataByDb) && !setupDataByDb.isEmpty()) {
			// 时间直接从字段获取，开机设置不需要走下发
			StringBuilder stringBuilder = new StringBuilder();
			String year = (String) setupDataByDb.get("year");
			String monthDay = (String) setupDataByDb.get("monthDay");
			String hoursMin = (String) setupDataByDb.get("hoursMin");
			String seconds = (String) setupDataByDb.get("seconds");
			StringBuilder append = stringBuilder.append(year).append("-").append(monthDay).append(" ").append(hoursMin).append(":").append(seconds);
			setupDataByDb.put(DATETIME, append.toString());
			R<String> r = super.determineDeviceStatus(appSetRequestVO.getPlantId());
			// 设备状态正常才去下发设备获取时间
			if (r == null) {
				// 最新的时间数据，下发请求从设备端获取；取不到则取数据库的旧数据
				JSONObject issueObj = new JSONObject();
				String requestId = TimeUtils.generateRequestId();
				issueObj.put("deviceSn", appSetRequestVO.getDeviceSerialNumber());
				issueObj.put("requestId", requestId);
				issueObj.put("topic", Constants.DEVICE_GET_TIME);
				Map<String, String> deviceTime = deviceIssueBiz.getDeviceTime(issueObj);
				if (ObjectUtil.isNotNull(deviceTime) && !deviceTime.isEmpty() && deviceTime.containsKey("200")) {
					setupDataByDb.put(DATETIME, deviceTime.get("200"));
				}
			}
			BigDecimal zeroDecimal = new BigDecimal("0.000");

			List<SetItemConfigEntity> setItemConfigEntity = setupItemConfig.getSetItemConfigEntityList();
			Map<String, SetItemConfigEntity> collect = setItemConfigEntity.stream().filter(e -> e.getSetItemKey().equals(RATED_VOLTAGE) || e.getSetItemKey().equals(RATED_FREQUENCY))
				.collect(Collectors.toMap(SetItemConfigEntity::getSetItemKey, e -> e));

			// 给这两个参数添加默认值
			if (ObjectUtil.isNotNull(setupDataByDb.get(RATED_VOLTAGE)) && zeroDecimal.equals(setupDataByDb.get(RATED_VOLTAGE)) && ObjectUtil.isNotNull(collect.get(RATED_VOLTAGE))) {
				SetItemConfigEntity entity = collect.get(RATED_VOLTAGE);
				setupDataByDb.put(RATED_VOLTAGE,entity.getSetItemDefault());
			}

			if (ObjectUtil.isNotNull(setupDataByDb.get(RATED_FREQUENCY)) && zeroDecimal.equals(setupDataByDb.get(RATED_FREQUENCY)) && ObjectUtil.isNotNull(collect.get(RATED_VOLTAGE))) {
				SetItemConfigEntity entity = collect.get(RATED_FREQUENCY);
				setupDataByDb.put(RATED_FREQUENCY,entity.getSetItemDefault());
			}

			for (Map.Entry<String, Object> entry : setupDataByDb.entrySet()) {

				super.buildSetupItemInfo(entry, setupItemConfig, dataObject);
			}
			super.buildTimeZoneInfo(dataObject,appSetRequestVO);
		}
	}

	@Override
	public R<String> issueSetupToToolKit(AppAdvancedSetup deviceAdvancedSetup) {
		R<String> result = device24Service.issueAdvancedSetup(deviceAdvancedSetup);
		log.info("set Advanced Setup : {}", result);
		return result;
	}

	@Override
    protected R<?> getReturnByMqttResult(AppAdvancedSetup deviceAdvancedSetup, R<String> result, Table<String, String, Object> table, List<AppAdvancedSetup.SetupItem> items) {
		if (result.getCode() == 200) {
			// 设置成功以后要对device21\23\24表进行修改，记录状态
			Map<String, Object> device24 = table.row("device_24");
			if (ObjectUtil.isNotEmpty(device24) && !device24.isEmpty()) {
				device24Service.updateSetup(device24, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}
			Map<String, Object> device23 = table.row("device_23");
			if (ObjectUtil.isNotEmpty(device23) && !device23.isEmpty()) {
				device23Service.updateSetup(device23, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}
			Map<String, Object> device21 = table.row("device_21");
			if (ObjectUtil.isNotEmpty(device21) && !device21.isEmpty()) {
				device21Service.updateSetup(device21, deviceAdvancedSetup.getPlantId(), deviceAdvancedSetup.getDeviceSerialNumber());
			}

			//根据下发类型处理不同的业务
			if (ObjectUtil.isNotNull(deviceAdvancedSetup.getIssueSetupType())) {
				// 开机设置成功以后，更新wifi表字段和device24表字段值
				// 如果设置项为inverter_control才需要更新
				// 0为通过平台开过机
				List<AppAdvancedSetup.SetupItem> setupItems = deviceAdvancedSetup.getSetupItems();
				for (AppAdvancedSetup.SetupItem setupItem : setupItems) {
					if ("inverterControl".equals(setupItem.getDefinition())) {
						WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
						wifiStickPlant.setPlantId(deviceAdvancedSetup.getPlantId());
						wifiStickPlant.setDeviceSerialNumber(deviceAdvancedSetup.getDeviceSerialNumber());
						wifiStickPlant.setStartupByBackstage(0);
						wifiStickPlant.setUpdateTime(new Date());
						wifiStickPlantService.updateStartupByBackstage(wifiStickPlant);

						Device24Entity device24Entity = new Device24Entity();
						device24Entity.setPlantId(deviceAdvancedSetup.getPlantId());
						device24Entity.setDeviceSerialNumber(deviceAdvancedSetup.getDeviceSerialNumber());
						device24Entity.setInverterControl("0");
						device24Entity.setUpdateTime(new Date());
						device24Service.updateInverterControl(device24Entity);
						break;
					}
				}

			}
			String currentLanguage = CommonUtil.getCurrentLanguage();
			return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
		} else if (result.getCode() == 100027) {
			// 把未设置成功设置项返回前端
			List<AppAdvancedSetup.SetupItem> errorItems = new ArrayList<>();
			String msg = result.getMsg();
			String[] arr = msg.split(",");
			HashSet<String> set = new HashSet<>(Arrays.asList(arr));
			String errorDateTime = "";
			for (AppAdvancedSetup.SetupItem item : items) {
				if (set.contains(item.getAddress().toString())) {
					errorItems.add(item);
				}
				if (item.getDefinition().equals(DATETIME)) {
					errorDateTime = (String) item.getData();
				}
			}
			if (set.contains(12288 + "")) {
				AppAdvancedSetup.SetupItem setupItem = new AppAdvancedSetup.SetupItem();
				setupItem.setDefinition(DATETIME);
				setupItem.setData(errorDateTime);
				errorItems.add(setupItem);
			}
			return R.data(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getCode(), errorItems, I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getMessage());
		}
		return result;
	}
}
