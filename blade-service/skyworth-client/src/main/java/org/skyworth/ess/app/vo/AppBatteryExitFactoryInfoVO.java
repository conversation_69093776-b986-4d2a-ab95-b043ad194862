package org.skyworth.ess.app.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppBatteryExitFactoryInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String batteryStatus;
    private String newQualityQuaranteeYear;
    private String batterySerialNumber;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
    private Long batteryMapDeviceId;
    private String ratedBatteryEnergy;
    // 出厂日期
    private Date exitFactoryDate;
    // 出厂日期 + 质保年限
    private String qualityQuaranteeDate;

	//质保年限
	private String qualityGuaranteeYear;

	//质保开始日期
	private String warrantyStartDate;
}
