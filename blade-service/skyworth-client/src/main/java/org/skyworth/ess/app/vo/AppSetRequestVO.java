package org.skyworth.ess.app.vo;

import lombok.Data;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/2/4 11:21:25
 */
@Data
public class AppSetRequestVO {
	private String deviceSerialNumber;

	private Long plantId;
	/**
	 * 设备类型0逆变器1电池2光伏组件3充电桩
	 */
	private Integer deviceType;
	/**
	 * 设备型号
	 */
	private String deviceModel;
	/**
	 * 设置项对应协议协议版本
	 */
	private String setItemProtocolVersion;
	/**
	 * 设置类型0高级设置，1开机设置，2分时设置，3设备参数，4模式设置
	 */
	private Integer setCategory;
	/**
	 * 设置项大类
	 */
	private String setItemBigType;

	/**
	 * 当前语言
	 */
	private String language;
}
