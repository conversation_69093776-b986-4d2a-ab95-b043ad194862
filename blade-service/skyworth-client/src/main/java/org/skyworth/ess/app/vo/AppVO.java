package org.skyworth.ess.app.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppVO implements Serializable {

	private static final long serialVersionUID = 1L;
	// 电池SN
	private String batterySerialNumber;
	// 逆变器SN
	private String deviceSerialNumber;
	// wifi棒SN
	private String wifiStickSerialNumber;
	// 站点ID
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	// 是否匹配
	private Boolean matchOrNot;
	// 当前用户
	private String currencyUser;
	// '报表类型0:日报表,1：周报表，2：月报表，3：年报表'
	private int type;
	// 数据范围
	private String dataScope;
	// 删除电池
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long batteryMapDeviceId;
	// 语言
	private String language;
	private String verificationCode;
	private String smsId;
	private String phoneDiallingCode;
	private String phone;

	// 验证类型，0邮箱，1手机
	private String verificationType;
	private String email;

	private String inverterKind;
	// 接口版本
	private String interfaceVersion;

	private Boolean forceAddNonExitBattery;
	// 设备时区
	private String timeZone;

	// 是否查并机数据 ，1 为是， 0 为否
	private String isParallelMode;

	private String listSearchCondition;

	private Integer pageSize;
	private Integer current;
}
