package org.skyworth.ess.app.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class InverterModeVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "字典码")
    private String code;

    /**
     * 字典值
     */
    @ApiModelProperty(value = "字典值")
    private String dictKey;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    private String dictValue;
    /**
     * 字典备注
     */
    @ApiModelProperty(value = "字典备注")
    private String remark;

    /**
     * 是否已封存
     */
    @ApiModelProperty(value = "是否已封存")
    private Integer isSealed;
    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "属性1")
    private String attribute1;

    @ApiModelProperty(value = "属性2")
    private String attribute2;
    @ApiModelProperty(value = "属性3")
    private String attribute3;
    // 当前模式
    private String currentDeivceMode;

}
