/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.fegin;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.client.IDeviceLog24Client;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.device.service.IDeviceLog24Service;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.LockType;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tenant.annotation.NonDS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 字典服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class DeviceLog24BizClient  implements IDeviceLog24Client {

	private IDeviceLog24Service deviceLog24Service;

	private BladeRedis redis;

	private IDeviceIssueBiz deviceIssue;

	private IDevice24Service device24Service;

	private IWifiStickPlantService wifiStickPlantService;

	private RedisLockClient client;

	@PostMapping(device24)
	@Override
	public void insertBatchDeviceLog24Column(List<DeviceLog24Entity> deviceLog24Entities) {
		log.info("DeviceLog24BizClient insert : {}",deviceLog24Entities);
		deviceLog24Service.saveBatch(deviceLog24Entities);
		List<String> snList = deviceLog24Entities.stream()
			.map(DeviceLog24Entity::getDeviceSerialNumber)
			.collect(Collectors.toList());

		//查出所有sn的信息
		Map<String, Device24Entity> finalDeviceMap = getEntityMap(snList);
		// 设备同一个 sn 上报的数据 可能在 相同的时间上有多条数据，取最新的一条
		List<DeviceLog24Entity> uniqueDeviceLog24Entities = deviceLog24Entities.stream()
				.collect(Collectors.collectingAndThen(
						Collectors.groupingBy(
								device -> new AbstractMap.SimpleEntry<>(device.getDeviceSerialNumber(), device.getPlantId()),
								Collectors.maxBy(Comparator.comparing(DeviceLog24Entity::getDeviceDateTime))
						),
						map -> map.values().stream()
								.filter(Optional::isPresent)
								.map(Optional::get)
								.collect(Collectors.toList())
				));

		ThreadPoolCustom.getCustomThreadPool().submit(() -> uniqueDeviceLog24Entities.forEach(v->{
            String deviceSn=v.getDeviceSerialNumber();
            long plantId=v.getPlantId();
			String key1=CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_1";
			String key3=CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_3";
            try{
				common(v, deviceSn,finalDeviceMap);
				String value1;
				String value3;
				int count=30;
				int j=1;
				boolean res=false;
				while (j<count){
					value1=redis.get(key1);
					value3=redis.get(key3);
					if(StringUtils.isEmpty(value1)||StringUtils.isEmpty(value3)){  //俩个其中有一个为空则继续等待
						Thread.sleep(500);
						++j;
					}else {
						res=true;
						break;
					}
				}
				if(res){ //成功
					int num=50;
					int i=1;
					String connectedFlag;
					boolean success=false;
					while (i<num) {
						connectedFlag = redis.get(CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION_NET+"_"+deviceSn);
						if(StringUtils.isEmpty(connectedFlag)){
							Thread.sleep(200);
							++i;
						}else {
							success=true;
							break;
						}
					}
					if(success){
						//发送配网成功给app
						distributionNetwork(deviceSn, plantId);
					}
				}
            }catch (Exception e){
                log.error("配网异常: "+e.getMessage());
            }finally {
				redis.del(CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION_NET+"_"+deviceSn);
				redis.del(key1);
				redis.del(key3);
			}


        }));

	}

	/**
	 * 新版本配网新逻辑
	 * */
	@PostMapping(NETWORK)
	@Override
	public void insertBatchDeviceLog24ForNetWork(List<DeviceLog24Entity> deviceLog24Entities) {
		deviceLog24Service.saveBatch(deviceLog24Entities);
		List<String> snList = deviceLog24Entities.stream()
			.map(DeviceLog24Entity::getDeviceSerialNumber)
			.collect(Collectors.toList());

		//查出所有sn的信息
		Map<String, Device24Entity> finalDeviceMap = getEntityMap(snList);
		deviceLog24Entities.stream().parallel().forEach(v->{
			String deviceSn=v.getDeviceSerialNumber();
			long plantId=v.getPlantId();
			common(v, deviceSn, finalDeviceMap);
			String key= CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION+plantId+"_"+deviceSn+"_v2_4";
			redis.setEx(key,"1",Duration.ofSeconds(60));
		});
	}

	private void distributionNetwork( String deviceSn, long plantId) {
		//需要进入开机设置
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("deviceSn", deviceSn);

		LambdaQueryWrapper<WifiStickPlantEntity> wifiEq = Wrappers.<WifiStickPlantEntity>query().lambda()
			.eq(WifiStickPlantEntity::getDeviceSerialNumber, deviceSn).eq(WifiStickPlantEntity::getPlantId, plantId) ;
		List<WifiStickPlantEntity> wifiStickPlantEntities=wifiStickPlantService.list(wifiEq);
		Integer startupByBackstage=1;
		if(wifiStickPlantEntities!=null && !wifiStickPlantEntities.isEmpty()){
			WifiStickPlantEntity wifiStickPlant=wifiStickPlantEntities.get(0);
			startupByBackstage=wifiStickPlant.getStartupByBackstage();
			wifiStickPlant.setInverterConfigureNetwork(BizConstant.CLIENT_INVERTER_CONFIGURE_NETWORK_YES);
			wifiStickPlantService.updateById(wifiStickPlant);
		}
		jsonObject.put("default_param_setting",startupByBackstage);
		jsonObject.put("topic",Constants.MSG_PUSH_APP);
		deviceIssue.dataIssueToDevice(jsonObject);
	}

	private void common(DeviceLog24Entity v, String deviceSn, Map<String, Device24Entity> deviceMap) {
		if(deviceMap.isEmpty()){
			Device24Entity device24Entity=new Device24Entity();
			org.springframework.beans.BeanUtils.copyProperties(v,device24Entity);
			device24Entity.setId(null);
			device24Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			device24Service.save(device24Entity);
		}else {
			Device24Entity device24Entity=deviceMap.get(deviceSn);
			if(ValidationUtil.isNotEmpty(device24Entity)){
				long id=device24Entity.getId();
				Date createTime = device24Entity.getCreateTime();
				org.springframework.beans.BeanUtils.copyProperties(v,device24Entity);
				log.info("DeviceLog24BizClient insert device24Entity : {}",device24Entity);
				device24Entity.setId(id);
				device24Entity.setCreateTime(createTime);
				device24Service.updateById(device24Entity);
			}else {
				device24Entity=new Device24Entity();
				org.springframework.beans.BeanUtils.copyProperties(v,device24Entity);
				device24Entity.setId(null);
				device24Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				device24Service.save(device24Entity);
			}
		}
	}

	@NotNull
	private Map<String, Device24Entity> getEntityMap(List<String> snList) {
		//查出所有sn的信息
		LambdaQueryWrapper<Device24Entity> eq = Wrappers.<Device24Entity>query().lambda()
			.in(Device24Entity::getDeviceSerialNumber, snList).eq(Device24Entity::getIsDeleted,0) ;
		List<Device24Entity> device24EntityList=device24Service.list(eq);
		Map<String, Device24Entity> deviceMap=new HashMap<>();
		if(ValidationUtil.isNotEmpty(device24EntityList)&&!device24EntityList.isEmpty()){
			deviceMap = device24EntityList.stream()
				.collect(Collectors.toMap(Device24Entity::getDeviceSerialNumber, Function.identity()));
		}

		return deviceMap;
	}
}
