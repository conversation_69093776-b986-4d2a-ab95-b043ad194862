package org.skyworth.ess.fegin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.mapper.AlarmLogMapper;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;
import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.service.IAlarmLogOperationRecordService;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.device.client.IErrorInfoClient;
import org.skyworth.ess.device.entity.AlarmTypeEnum;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.util.JpushUtil;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@NonDS
@ApiIgnore
@RestController
@Slf4j
@RequiredArgsConstructor
public class ErrorInfoBizClient extends BaseServiceImpl<AlarmLogMapper, AlarmLogEntity> implements IErrorInfoClient {

	@Autowired
	private IDevice21Service device21Service;

	@Autowired
	private IBatteryCurrentStatusService currentStatusService;

	@Autowired
	private IPlantService plantService;

	@Autowired
	private BladeRedis redis;

	@Autowired
	private JpushUtil jpushUtil;

	@Autowired
	private IDictBizClient dictBizClient;

	@Value("${blade.localLanguage}")
	private String language;

	@Value("${blade.env}")
	private String env;

	private final IWifiStickPlantService wifiStickPlantService;

	private final IAlarmLogOperationRecordService alarmLogOperationRecordService;

	private final static String URL_INTENT_ANDROID = "intent:#Intent;action=android.intent.action.pushaction;" +
		"component=${packageName}/com.skyworth.module_main.ui.activity.PlantActivity;S.plant_id=${plantId};S" +
		".plant_name=${plantName};S.deviceSerialNumber=${deviceSn};S.device_model=${deviceModel};end";


	@PostMapping(address)
	@Override
	public R<String> errorInfo(@RequestBody Map<String, Map<String, Object>> error) {
		log.info("ErrorInfoBizClient begin,errorInfo:" + error);
		//正常的设备
		Map<String, String> normalDeviceStatus = new HashMap<>();
		//异常的设备
		Set<String> abnormalDeviceStatus = new HashSet<>();
		long plantId = 0L;
		String deviceSerialNumber = null;
		String batteryStatus = "";
		List<Long> plantIdList = new ArrayList<>();
		for (Map.Entry<String, Map<String, Object>> entry : error.entrySet()) {
			Map<String, Object> values = entry.getValue();
			plantId = (long) values.get("plantId");
			deviceSerialNumber = (String) values.get("deviceSn");
			plantIdList.add(plantId);
		}
		// 查询站点信息
		List<PlantEntity> plantEntityList =
			plantService.list(Wrappers.<PlantEntity>lambdaQuery().in(PlantEntity::getId, plantIdList));
		Map<Long, PlantEntity> plantEntityMap =
			Optional.ofNullable(plantEntityList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(PlantEntity::getId, Function.identity(), (a, b) -> a));
		// 存储告警信息集合
		List<AlarmLogEntity> logEntities = new ArrayList<>();
		// 设置个人异常信息标识
		Set<String> existUserAlarmTypeSet = new HashSet<>();
		Set<String> existAgentAlarmTypeSet = new HashSet<>();
		Set<String> deviceAbnormalSet = new HashSet<>();
		Set<String> batteryAbnormalSet = new HashSet<>();
		Set<String> currentStatusKeys = new HashSet<>();
		Set<String> redisExistKeys = redis.keys(plantId + ":" + deviceSerialNumber + ":*");
		// 遍历异常信息
		for (Map.Entry<String, Map<String, Object>> entry : error.entrySet()) {
			String key = entry.getKey();
			Map<String, Object> values = entry.getValue();
			deviceSerialNumber = (String) values.get("deviceSn");
			String errorMsg = (String) values.get("exceptionMessage");
			String type = (String) values.get("type");
			plantId = (long) values.get("plantId");
			String groupKey = plantId + "@" + type + "@" + deviceSerialNumber;
			// 获取上报时间(转换为UTC+0后的时间)
			Date deviceDateTime = DateUtil.parse(values.get("deviceDateTime").toString(), DateUtil.PATTERN_DATETIME);
			// 获取标准时区(统一为标准时区UTC+0)
			String timeZone = (String) values.get("timeZone");
			List<Integer> indexes = getIndex(errorMsg, '1');
			// 电池1、2告警
			if (Constants.ERROR_CODE_MESSAGE.equals(key)) {
				Object tmp = values.get("batteryCurrent");
				if (tmp != null) {
					batteryStatus = (String) values.get("batteryCurrent");
				}
			}
			this.judgeDeviceOrBatteryNormal(key, indexes, deviceAbnormalSet, batteryAbnormalSet);
			//为空则为正常
			if (indexes.isEmpty()) {
				normalDeviceStatus.put(key, deviceSerialNumber);
			} else {
				// 不为空则为异常,封装异常信息对象
				encapsulateAbnormalList(indexes, key, deviceSerialNumber, deviceDateTime, plantId, type, timeZone,
					logEntities, plantEntityMap, groupKey, existUserAlarmTypeSet, existAgentAlarmTypeSet,
					currentStatusKeys);
				//智能能量变换器  有三条数据
				if (AlarmTypeEnum.DEVICE.getType().equals(type)) {
					abnormalDeviceStatus.add(deviceSerialNumber);
				}
			}
		}
		// 这是处理重复告警报文的场景，避免往下执行
		if (!redisExistKeys.isEmpty() && redisExistKeys.size() == currentStatusKeys.size() && redisExistKeys.containsAll(currentStatusKeys)) {
			return R.success("操作成功");
		}
		//有异常数据
		if (!logEntities.isEmpty()) {
			// 不在该报警范围的报警信息，需要自动关闭
			/*alarmRecoveryAndLogging(logEntities);*/
			// 保存新的告警数据
			this.saveBatch(logEntities);
			// 异步处理保存消息，并极光推送相应责任人,
			// 业务调整：2024.11.20号调整，取消极光推送消息到消息中心，取消消息功能
			/*long finalPlantId = plantId;
			// 构建安装商部门ID字符串，用于查询相应的人员列表
			String departmentIdListStr = Optional.ofNullable(plantEntityList).orElse(new ArrayList<>()).stream()
				.map(a -> Func.toStr(a.getOperationCompanyId())).filter(Objects::nonNull).distinct()
				.collect(Collectors.joining(","));
			ThreadPoolCustom.getCustomThreadPool().submit(() -> saveMessageAndPush(logEntities,
				departmentIdListStr, plantEntityMap.get(finalPlantId)));*/
		}
		// 关闭redis告警，以及新增告警修复记录
		turnOffAlarmReduceRecord(currentStatusKeys, plantId, deviceSerialNumber);
		// 更新逆变器状态为故障
		updateDeviceStatusToFault(abnormalDeviceStatus, plantId, existUserAlarmTypeSet, existAgentAlarmTypeSet);
		// 更新逆变器&电池状态为在线
		updateDeviceStatusToOnline(normalDeviceStatus, plantId, abnormalDeviceStatus);
		// 更新电池状态
		updateBatteryInfo(deviceSerialNumber, plantId, batteryStatus, existUserAlarmTypeSet,
			existAgentAlarmTypeSet, batteryAbnormalSet);
		// 更新站点状态,站点状态需要考虑并机情况下多台机器告警
		updatePlantStatus(deviceSerialNumber, plantId, deviceAbnormalSet, batteryAbnormalSet);
		return R.success("操作成功");
	}

	/**
	 * 处理特殊情况，更新电池状态
	 *
	 * @param currentStatusKeys  当前上报异常设备状态字符串集合
	 * @param plantId            电池状态
	 * @param deviceSerialNumber 逆变器SN
	 */
	private void turnOffAlarmReduceRecord(Set<String> currentStatusKeys, long plantId,
										  String deviceSerialNumber) {
		// 检查当前状态键是否为空，如果为空，则关闭设备和电池的告警
		if (currentStatusKeys.isEmpty()) {
			// 关闭逆变器告警
			equipmentStatusRecoveryUpdateAlarmStatus(plantId, AlarmTypeEnum.DEVICE.getType(), deviceSerialNumber);
			// 关闭电池告警
			equipmentStatusRecoveryUpdateAlarmStatus(plantId, AlarmTypeEnum.BATTERY.getType(), deviceSerialNumber);
			return;
		}
		// 查询数据库存在的未关闭的告警key
		Set<String> mysqlExistKeys = baseMapper.getDistinctKeys(plantId, deviceSerialNumber);
		// 检查Redis中存在的键是否为空，如果为空，则直接返回
		if (mysqlExistKeys.isEmpty()) {
			return;
		}
		// 创建一个集合用于存储需要删除的键
		Set<String> keysToDelete = new HashSet<>();
		// 遍历Redis中存在的键，找出需要删除的键
		mysqlExistKeys.forEach(key -> {
			if (!currentStatusKeys.contains(key)) {
				keysToDelete.add(key);
			}
		});
		// 如果没有需要删除的键，则直接返回
		if (keysToDelete.isEmpty()) {
			return;
		}
		List<AlarmLogEntity> alarmLogEntityList = new ArrayList<>();
		keysToDelete.forEach(key -> {
			AlarmLogEntity alarmLogEntity = new AlarmLogEntity();
			String[] keys = key.split(":");
			alarmLogEntity.setAddressCode(keys[3]);
			alarmLogEntity.setAlarmNumber(Integer.parseInt(keys[4]));
			alarmLogEntityList.add(alarmLogEntity);
		});
		// 查询需要删除的记录列表
		List<AlarmLogPageVO> deleteRecordList = baseMapper.queryDeleteRecord(plantId + "",
			deviceSerialNumber,
			alarmLogEntityList);
		// 如果查询结果为空，则直接返回
		if (deleteRecordList == null || deleteRecordList.isEmpty()) {
			return;
		}
		// 提取记录的ID列表
		List<Long> recordIds = deleteRecordList.stream()
			.map(AlarmLogPageVO::getId)
			.collect(Collectors.toList());
		// 插入记录并更新状态
		insertRecordAndUpdateStatus(recordIds);
		// 删除缓存中的键
		delCacheKey(keysToDelete);
	}

	/**
	 * 更新电池状态
	 *
	 * @param deviceSerialNumber     设备SN
	 * @param plantId                站点id
	 * @param existUserAlarmTypeSet  用户告警类型集合
	 * @param existAgentAlarmTypeSet 安装商告警类型集合
	 * @param batteryAbnormalSet     电池异常SN集合
	 */
	private void updateBatteryInfo(String deviceSerialNumber, long plantId, String batteryStatus,
								   Set<String> existUserAlarmTypeSet, Set<String> existAgentAlarmTypeSet,
								   Set<String> batteryAbnormalSet) {
		// 如果电池没有异常，则更新为 电池 对应的状态： 充电、放电、在线 等
		if (batteryAbnormalSet.isEmpty()) {
			this.updateBatteryStatusToNormal(deviceSerialNumber, plantId, batteryStatus);
			return;
		}
		// 如果异常不为空，则更新电池异常
		this.updateBatteryStatusToAbnormal(deviceSerialNumber, plantId, existUserAlarmTypeSet, existAgentAlarmTypeSet,
			batteryAbnormalSet);
	}

	/**
	 * 更新电池状态为异常
	 *
	 * @param deviceSerialNumber     设备SN
	 * @param plantId                站点id
	 * @param existUserAlarmTypeSet  用户告警类型集合
	 * @param existAgentAlarmTypeSet 安装商告警类型集合
	 * @param batteryAbnormalSet     电池异常SN集合
	 */
	private void updateBatteryStatusToAbnormal(String deviceSerialNumber, long plantId,
											   Set<String> existUserAlarmTypeSet, Set<String> existAgentAlarmTypeSet,
											   Set<String> batteryAbnormalSet) {
		//储能包  只有一条数据
		LambdaQueryWrapper<BatteryCurrentStatusEntity> eq =
			Wrappers.<BatteryCurrentStatusEntity>query().lambda()
				.eq(BatteryCurrentStatusEntity::getDeviceSerialNumber, deviceSerialNumber).eq(BatteryCurrentStatusEntity::getPlantId, plantId);
		List<BatteryCurrentStatusEntity> batteryCurrentStatusEntities = currentStatusService.list(eq);
		if (!batteryCurrentStatusEntities.isEmpty()) {
			BatteryCurrentStatusEntity batteryCurrentStatus =
				getBatteryCurrentStatusEntity(batteryCurrentStatusEntities, existUserAlarmTypeSet,
					existAgentAlarmTypeSet, batteryAbnormalSet, deviceSerialNumber, plantId);
			currentStatusService.updateById(batteryCurrentStatus);
		}
	}

	/**
	 * 更新电池状态为正常
	 *
	 * @param deviceSerialNumber 设备SN
	 * @param plantId            站点id
	 * @param batteryStatus      电池当前电流
	 */
	private void updateBatteryStatusToNormal(String deviceSerialNumber, long plantId, String batteryStatus) {
		//储能包  则更新为 电池 对应的状态： 充电、放电、在线 等
		LambdaQueryWrapper<BatteryCurrentStatusEntity> eq =
			Wrappers.<BatteryCurrentStatusEntity>query().lambda()
				.eq(BatteryCurrentStatusEntity::getDeviceSerialNumber, deviceSerialNumber).eq(BatteryCurrentStatusEntity::getPlantId, plantId);
		List<BatteryCurrentStatusEntity> batteryCurrentStatusEntities = currentStatusService.list(eq);
		// 如果电池状态存在且有当前电流信息，则更新电池状态
		if (!batteryCurrentStatusEntities.isEmpty() && StringUtils.isNotEmpty(batteryStatus)) {
			BatteryCurrentStatusEntity batteryCurrentStatus = batteryCurrentStatusEntities.get(0);
			// 设置储能包状态
			batteryCurrentStatus.setBatteryStatus(batteryStatus);
			// 设置是否存在用户异常标识为0-不存在
			batteryCurrentStatus.setExistUserTypeAlarm(BizConstant.NUMBER_ZERO);
			batteryCurrentStatus.setExistAgentTypeAlarm(BizConstant.NUMBER_ZERO);
			currentStatusService.updateById(batteryCurrentStatus);
			// 关闭储能包告警
			//equipmentStatusRecoveryUpdateAlarmStatus(plantId, AlarmTypeEnum.BATTERY.getType(), deviceSerialNumber);
		}
	}

	private void judgeDeviceOrBatteryNormal(String errorAddress, List<Integer> errorCodeList,
											Set<String> deviceAbnormalSet, Set<String> batteryAbnormalSet) {
		if (Constants.ERROR_CODE_MESSAGE.equals(errorAddress)) {
			if (!errorCodeList.isEmpty()) {
				batteryAbnormalSet.add(errorAddress);
			}
		} else if (Constants.ERROR_CODE.equals(errorAddress) || Constants.ERROR_CODE2.equals(errorAddress) || Constants.ERROR_CODE3.equals(errorAddress)) {
			if (!errorCodeList.isEmpty()) {
				deviceAbnormalSet.add(errorAddress);
			}
		}
	}

	/**
	 * 更新站点状态
	 *
	 * @param deviceSerialNumber 设备序列号
	 * @param plantId            站点ID
	 * @param deviceAbnormalSet  设备异常集合
	 * @param batteryAbnormalSet 电池异常集合
	 */
	private void updatePlantStatus(String deviceSerialNumber, long plantId,
								   Set<String> deviceAbnormalSet, Set<String> batteryAbnormalSet) {
		// 获取站点的告警信息，包括用户类型和代理类型的告警状态以及站点状态
		Map<String, Object> alarmInfo = getAlarmInfo(plantId);
		// 创建查询条件，查找与给定设备序列号和站点ID匹配的WifiStickPlantEntity记录
		LambdaQueryWrapper<WifiStickPlantEntity> wifiStickPlantEntityEq =
			Wrappers.<WifiStickPlantEntity>query().lambda()
				.eq(WifiStickPlantEntity::getDeviceSerialNumber, deviceSerialNumber)
				.eq(WifiStickPlantEntity::getPlantId, plantId);
		// 查询逆变器是否在站点存在
		long deviceSerialNumberCount = wifiStickPlantService.count(wifiStickPlantEntityEq);
		// 如果查询结果不为空，则继续处理
		if (deviceSerialNumberCount > 0) {
			// 如果设备异常集合和电池异常集合都为空
			if (deviceAbnormalSet.isEmpty() && batteryAbnormalSet.isEmpty()) {
				// 更新数据库中的PlantEntity记录
				plantService.update(Wrappers.lambdaUpdate(PlantEntity.class).set(PlantEntity::getPlantStatus,
						alarmInfo.get("plantStatus")).set(PlantEntity::getExistUserTypeAlarm, alarmInfo.get(
						"existUserTypeAlarm"))
					.set(PlantEntity::getExistAgentTypeAlarm, alarmInfo.get("existAgentTypeAlarm")
					).eq(PlantEntity::getId, plantId)
				);
			} else {
				// 如果存在设备或电池异常，则直接更新站点状态为异常
				plantService.updateStatusById(plantId, BizConstant.CHAR_TWO,
					(int) alarmInfo.get("existUserTypeAlarm"), (int) alarmInfo.get("existAgentTypeAlarm"));
			}
		}
	}

	/**
	 * 获取告警信息并判断是否存在用户类型和代理类型的告警
	 *
	 * @param plantId 站点ID
	 * @return 包含用户类型和代理类型告警信息的 Map
	 */
	private Map<String, Object> getAlarmInfo(long plantId) {
		// 查询告警日志，获取与指定站点ID相关的告警信息
		List<AlarmLogEntity> alarmLogEntityList =
			baseMapper.selectList(Wrappers.<AlarmLogEntity>lambdaQuery()
				.select(AlarmLogEntity::getId, AlarmLogEntity::getUserId, AlarmLogEntity::getDepartmentId)
				.eq(AlarmLogEntity::getPlantId, plantId)
				.eq(AlarmLogEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED)
				.in(AlarmLogEntity::getExceptionType,
					Arrays.asList(AlarmTypeEnum.DEVICE.getType(), AlarmTypeEnum.BATTERY.getType()))
				.ne(AlarmLogEntity::getStatus, BizConstant.NUMBER_ONE)
			);
		int existUserTypeAlarm = 0, existAgentTypeAlarm = 0;
		// 默认站点状态在线
		String plantStatus = BizConstant.CHAR_ONE;
		// 如果存在告警日志，则计算用户类型和代理类型的告警数量
		if (!alarmLogEntityList.isEmpty()) {
			long existUserAlarmCount =
				alarmLogEntityList.stream().filter(alarmLogEntity -> alarmLogEntity.getUserId() != null).count();
			long existAgentAlarmCount =
				alarmLogEntityList.stream().filter(alarmLogEntity -> alarmLogEntity.getDepartmentId() != null).count();
			// 根据告警数量设置对应的标志位
			existUserTypeAlarm = existUserAlarmCount > BizConstant.NUMBER_ZERO ? BizConstant.NUMBER_ONE :
				BizConstant.NUMBER_ZERO;
			existAgentTypeAlarm = existAgentAlarmCount > BizConstant.NUMBER_ZERO ? BizConstant.NUMBER_ONE :
				BizConstant.NUMBER_ZERO;
			// 如果存在任何告警，将站点状态设置为异常
			plantStatus = BizConstant.CHAR_TWO;
		}
		// 将告警信息存入Map中返回
		Map<String, Object> alarmInfo = new HashMap<>();
		alarmInfo.put("existUserTypeAlarm", existUserTypeAlarm);
		alarmInfo.put("existAgentTypeAlarm", existAgentTypeAlarm);
		alarmInfo.put("plantStatus", plantStatus);
		return alarmInfo;
	}


	/**
	 * 更新设备或电池状态为在线
	 * <p>
	 * 此方法的目的是根据正常设备状态映射更新电池或设备的状态为在线，并在必要时重置异常状态标志
	 * 它还负责在更新状态后关闭相关的告警
	 *
	 * @param normalDeviceStatus   正常设备状态的映射，键是设备类型，值是设备序列号
	 * @param plantId              电厂ID，用于查询和更新设备状态
	 * @param abnormalDeviceStatus 异常设备状态的集合，用于判断是否需要更新设备状态
	 */
	private void updateDeviceStatusToOnline(Map<String, String> normalDeviceStatus, long plantId,
											Set<String> abnormalDeviceStatus) {
		// 标志位，用于确保在没有异常设备状态时只更新一次智能能量变换器的状态
		boolean flag = false;
		// 遍历正常设备状态映射
		for (Map.Entry<String, String> entry : normalDeviceStatus.entrySet()) {
			String key = entry.getKey();
			String deviceSn = entry.getValue();
			// 智能能量变换器处理
			if ("101E".equals(key) || "101F".equals(key) || "1020".equals(key)) {
				// 在没有异常设备状态且标志位未设置时更新智能能量变换器状态
				if (abnormalDeviceStatus.isEmpty() && !flag) {
					// 查询智能能量变换器当前状态
					LambdaQueryWrapper<Device21Entity> eq = Wrappers.<Device21Entity>query().lambda()
						.eq(Device21Entity::getDeviceSerialNumber, deviceSn).eq(Device21Entity::getPlantId,
							plantId);
					List<Device21Entity> device21EntityList = device21Service.list(eq);
					// 如果智能能量变换器状态存在，则更新状态
					if (!device21EntityList.isEmpty()) {
						Device21Entity device21 = device21EntityList.get(0);
						// 设置是否存在用户异常标识为0-不存在
						device21.setExistUserTypeAlarm(BizConstant.NUMBER_ZERO);
						device21.setExistAgentTypeAlarm(BizConstant.NUMBER_ZERO);
						// 设置为在线
						device21.setDeviceStatus("1");
						device21Service.updateById(device21);
						// 设置标志位，确保只更新一次
						flag = true;
					}
				}
			}
		}
	}

	/**
	 * 更新逆变器状态为故障
	 *
	 * @param abnormalDeviceStatus   故障SN集合
	 * @param plantId                站点ID
	 * @param existUserAlarmTypeSet  个人告警集合
	 * @param existAgentAlarmTypeSet 代理商告警集合
	 * <AUTHOR>
	 * @date 2025/4/17 15:40
	 */
	private void updateDeviceStatusToFault(Set<String> abnormalDeviceStatus, long plantId,
										   Set<String> existUserAlarmTypeSet, Set<String> existAgentAlarmTypeSet) {
		//更新智能能量变换器状态为故障
		for (String deviceSn : abnormalDeviceStatus) {
			LambdaQueryWrapper<Device21Entity> eq = Wrappers.<Device21Entity>query().lambda()
				.eq(Device21Entity::getDeviceSerialNumber, deviceSn).eq(Device21Entity::getPlantId, plantId);
			List<Device21Entity> device21EntityList = device21Service.list(eq);
			if (!device21EntityList.isEmpty()) {
				Device21Entity device21 = device21EntityList.get(0);
				//设置为故障
				device21.setDeviceStatus("2");
				String groupKey = plantId + "@" + BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER + "@" + deviceSn;
				device21.setExistUserTypeAlarm(existUserAlarmTypeSet.contains(groupKey) ? BizConstant.NUMBER_ONE :
					BizConstant.NUMBER_ZERO);
				device21.setExistAgentTypeAlarm(existAgentAlarmTypeSet.contains(groupKey) ? BizConstant.NUMBER_ONE :
					BizConstant.NUMBER_ZERO);
				device21Service.updateById(device21);
			}
		}
	}

	/**
	 * 获取电池当前状态实体
	 *
	 * @param batteryCurrentStatusEntities 电池当前状态实体列表
	 * @param existUserAlarmTypeSet        用户已存在的报警类型集合
	 * @param existAgentAlarmTypeSet       代理已存在的报警类型集合
	 * @param batteryAbnormalSet           电池异常集合
	 * @param deviceSerialNumber           设备序列号
	 * @param plantId                      植物ID
	 * @return 电池当前状态实体
	 */
	@NotNull
	private BatteryCurrentStatusEntity getBatteryCurrentStatusEntity(
		List<BatteryCurrentStatusEntity> batteryCurrentStatusEntities,
		Set<String> existUserAlarmTypeSet,
		Set<String> existAgentAlarmTypeSet,
		Set<String> batteryAbnormalSet,
		String deviceSerialNumber,
		long plantId) {
		BatteryCurrentStatusEntity batteryCurrentStatus = batteryCurrentStatusEntities.get(0);
		// 如果存在电池故障，则设置电池状态为故障
		if (!batteryAbnormalSet.isEmpty()) {
			batteryCurrentStatus.setBatteryStatus(BizConstant.CHAR_FOUR);
		}
		// 设置报警标识
		setAlarmFlags(batteryCurrentStatus, existUserAlarmTypeSet, existAgentAlarmTypeSet, deviceSerialNumber, plantId
		);
		return batteryCurrentStatus;
	}

	/**
	 * 设置报警标识
	 *
	 * @param batteryCurrentStatus   电池当前状态实体
	 * @param existUserAlarmTypeSet  用户已存在的报警类型集合
	 * @param existAgentAlarmTypeSet 代理已存在的报警类型集合
	 * @param deviceSerialNumber     设备序列号
	 * @param plantId                植物ID
	 */
	private void setAlarmFlags(BatteryCurrentStatusEntity batteryCurrentStatus,
							   Set<String> existUserAlarmTypeSet,
							   Set<String> existAgentAlarmTypeSet,
							   String deviceSerialNumber,
							   long plantId) {
		String groupKey =
			plantId + "@" + AlarmTypeEnum.BATTERY.getType() + "@" + deviceSerialNumber + "@" + Constants.ERROR_CODE_MESSAGE;
		int userAlarmFlag = existUserAlarmTypeSet.contains(groupKey) ? BizConstant.NUMBER_ONE :
			BizConstant.NUMBER_ZERO;
		int agentAlarmFlag = existAgentAlarmTypeSet.contains(groupKey) ? BizConstant.NUMBER_ONE :
			BizConstant.NUMBER_ZERO;
		batteryCurrentStatus.setExistUserTypeAlarm(userAlarmFlag);
		batteryCurrentStatus.setExistAgentTypeAlarm(agentAlarmFlag);
	}


	/**
	 * 封装异常信息列表
	 *
	 * @param indexes            异常索引列表
	 * @param key                地址代码
	 * @param deviceSerialNumber 设备序列号
	 * @param deviceDateTime     设备日期时间
	 * @param plantId            植物ID
	 * @param type               异常类型
	 * @param timeZone           时区
	 * @param logEntities        报警日志实体列表
	 * @param plantEntityMap     植物实体映射
	 */
	private void encapsulateAbnormalList(List<Integer> indexes, String key, String deviceSerialNumber,
										 Date deviceDateTime, long plantId, String type,
										 String timeZone, List<AlarmLogEntity> logEntities,
										 Map<Long, PlantEntity> plantEntityMap, String groupKey,
										 Set<String> existUserAlarmTypeSet, Set<String> existAgentAlarmTypeSet,
										 Set<String> currentStatusKeys) {
		try {
			// 获取异常等级字典
			List<DictBiz> dictBizList = DictBizCache.getListByParentCode(
				DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode(), key, CommonConstant.CURRENT_LANGUAGE_ZH);
			if (dictBizList == null || dictBizList.isEmpty()) {
				log.warn("No dictionary data found for key: {}", key);
				return;
			}
			Map<String, DictBiz> dictBizMap = dictBizList.stream()
				.collect(Collectors.toMap(DictBiz::getDictKey, Function.identity(), (k1, k2) -> k1));
			// 预先检查 PlantEntity 是否存在
			PlantEntity plant = plantEntityMap.get(plantId);
			if (plant == null) {
				log.warn("PlantEntity not found for plantId: {}", plantId);
				return;
			}
			// 预先构造 Redis 缓存键的前缀
			String redisKeyPrefix = plantId + ":" + deviceSerialNumber + ":" + type + ":" + key;
			// 遍历异常索引列表
			for (Integer code : indexes) {
				String msgKey = redisKeyPrefix + ":" + code;
				// 存储本次状态的缓存键
				currentStatusKeys.add(msgKey);
				// 创建告警日志实体
				AlarmLogEntity logEntity = createAlarmLogEntity(
					plantId, key, deviceSerialNumber, deviceDateTime, type, timeZone, code, groupKey);
				// 获取异常等级信息
				DictBiz dictBiz = dictBizMap.get(String.valueOf(code));
				if (dictBiz == null) {
					log.warn("DictBiz not found for code: {}", code);
					continue;
				}
				// 设置异常消息和等级
				logEntity.setExceptionMessage(dictBiz.getDictValue());
				logEntity.setAlarmLevel(dictBiz.getAttribute1());
				// 处理用户推送逻辑
				handleUserPush(logEntity, plant, groupKey, key, dictBiz, existUserAlarmTypeSet, type);
				// 处理安装商推送逻辑
				handleAgentPush(logEntity, plant, groupKey, key, dictBiz, existAgentAlarmTypeSet, type);
				//如果缓存中不存该异常，则添加到列表中,缓存一天结束
				if (ValidationUtil.isEmpty(redis.get(msgKey))) {
					redis.setEx(msgKey, LocalDate.now().toString(), Duration.ofDays(1));
					logEntities.add(logEntity);
				}
			}
		} catch (Exception e) {
			log.error("Error occurred while processing abnormal list", e);
		}
	}

	// 创建告警日志实体
	private AlarmLogEntity createAlarmLogEntity(long plantId, String key, String deviceSerialNumber,
												Date deviceDateTime, String type, String timeZone,
												int code, String groupKey) {
		AlarmLogEntity logEntity = new AlarmLogEntity();
		logEntity.setPlantId(plantId);
		logEntity.setAddressCode(key);
		logEntity.setSerialNumber(deviceSerialNumber);
		logEntity.setDeviceDateTime(deviceDateTime);
		logEntity.setExceptionType(type);
		logEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		logEntity.setTimeZone(timeZone);
		logEntity.setStatus(BizConstant.NUMBER_ZERO);
		logEntity.setAlarmNumber(code);
		logEntity.setGroupKey(groupKey);
		return logEntity;
	}

	// 处理用户推送逻辑
	private void handleUserPush(AlarmLogEntity logEntity, PlantEntity plant, String groupKey, String key,
								DictBiz dictBiz, Set<String> existUserAlarmTypeSet, String type) {
		if (CommonConstant.FLAG_Y.equalsIgnoreCase(dictBiz.getAttribute2())) {
			logEntity.setUserId(plant.getCreateUser());
			if (AlarmTypeEnum.BATTERY.getType().equalsIgnoreCase(type)) {
				existUserAlarmTypeSet.add(groupKey + "@" + key);
			} else {
				existUserAlarmTypeSet.add(groupKey);
			}
		}
	}

	// 处理安装商推送逻辑
	private void handleAgentPush(AlarmLogEntity logEntity, PlantEntity plant, String groupKey, String key,
								 DictBiz dictBiz, Set<String> existAgentAlarmTypeSet, String type) {
		if (CommonConstant.FLAG_Y.equalsIgnoreCase(dictBiz.getAttribute3())) {
			Long operationCompanyId = plant.getOperationCompanyId();
			if (operationCompanyId != null) {
				logEntity.setDepartmentId(operationCompanyId);
				if (AlarmTypeEnum.BATTERY.getType().equalsIgnoreCase(type)) {
					existAgentAlarmTypeSet.add(groupKey + "@" + key);
				} else {
					existAgentAlarmTypeSet.add(groupKey);
				}
			}
		}
	}


	public static List<Integer> getIndex(String str, char targetChar) {
		List<Integer> res = new ArrayList<>();
		int j = 0;
		for (int i = str.length() - 1; i >= 0; i--) {
			if (str.charAt(i) == targetChar) {
				res.add(str.length() - i - 1);
				++j;
			}
		}
		return res;
	}

	/**
	 * 状态从告警变为正常，把之前的告警需要自动关闭
	 *
	 * @param plantId       站点id
	 * @param exceptionType 异常类型
	 * @param deviceSn      入参
	 * <AUTHOR>
	 * @since 2024/11/21 11:20
	 **/
	private void equipmentStatusRecoveryUpdateAlarmStatus(Long plantId, String exceptionType, String deviceSn) {
		List<AlarmLogEntity> updateRecordIds =
			this.list(Wrappers.<AlarmLogEntity>lambdaQuery().select(AlarmLogEntity::getId).eq(AlarmLogEntity::getPlantId, plantId).eq(AlarmLogEntity::getSerialNumber, deviceSn)
				.eq(AlarmLogEntity::getExceptionType, exceptionType).ne(AlarmLogEntity::getStatus,
					BizConstant.NUMBER_ONE).eq(AlarmLogEntity::getIsDeleted, BizConstant.NUMBER_ZERO));
		if (ValidationUtil.isEmpty(updateRecordIds)) {
			return;
		}
		// 新增操作记录，并维护告警状态
		insertRecordAndUpdateStatus(updateRecordIds.stream().map(BaseEntity::getId).distinct().collect(Collectors.toList()));
		// 删除缓存异常key
		String msgKey = plantId + ":" + deviceSn + ":" + exceptionType;
		delBatchByCacheKey(msgKey);
	}

	/**
	 * 批量删除缓存
	 *
	 * @param key 入参
	 * <AUTHOR>
	 * @date 2024/12/13 10:43
	 */
	private void delBatchByCacheKey(String key) {
		Set<String> allAddressMapDefinitionByAddress = redis.keys(key + "*");
		redis.del(allAddressMapDefinitionByAddress);
	}


	/**
	 * 删除缓存key
	 *
	 * @param setKeys 入参
	 * <AUTHOR>
	 * @date 2024/12/13 11:05
	 */
	private void delCacheKey(Set<String> setKeys) {
		redis.del(setKeys);
	}

	/**
	 * 新增操作记录，并维护告警状态
	 *
	 * @param updateRecordIds 入参
	 * <AUTHOR>
	 * @since 2024/11/21 11:35
	 **/
	private void insertRecordAndUpdateStatus(List<Long> updateRecordIds) {
		// 创建报警操作记录实体列表
		List<AlarmLogOperationRecordEntity> alarmLogOperationRecordEntityList =
			createAlarmLogOperationRecordList(updateRecordIds);
		// 更新报警状态
		updateAlarmStatus(updateRecordIds);
		// 保存报警操作记录
		alarmLogOperationRecordService.saveBatch(alarmLogOperationRecordEntityList);
	}

	/**
	 * 根据更新记录ID创建报警操作记录列表
	 *
	 * @param updateRecordIds 更新记录ID列表
	 * @return 报警操作记录列表
	 */
	private List<AlarmLogOperationRecordEntity> createAlarmLogOperationRecordList(List<Long> updateRecordIds) {
		return updateRecordIds.stream()
			.map(a -> new AlarmLogOperationRecordEntity(a, "Automatische Wiederherstellung nach Systemüberprüfung",
				CommonConstant.COMMON_SYSTEM_USER_ACCOUNT,
				CommonConstant.COMMON_SYSTEM_USER_ACCOUNT))
			.peek(alarmLogOperationRecord -> alarmLogOperationRecord.setStatus(BizConstant.NUMBER_ONE))
			.collect(Collectors.toList());
	}

	/**
	 * 更新报警状态
	 *
	 * @param updateRecordIds 更新记录ID列表
	 */
	private void updateAlarmStatus(List<Long> updateRecordIds) {
		this.update(Wrappers.<AlarmLogEntity>lambdaUpdate()
			.set(AlarmLogEntity::getStatus, BizConstant.NUMBER_ONE)
			.set(AlarmLogEntity::getUpdateTime, DateUtil.now())
			.set(AlarmLogEntity::getUpdateUserAccount, CommonConstant.COMMON_SYSTEM_USER_ACCOUNT)
			.in(AlarmLogEntity::getId, updateRecordIds));
	}
}
