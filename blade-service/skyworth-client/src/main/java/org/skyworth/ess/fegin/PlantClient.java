package org.skyworth.ess.fegin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.permissions.entity.AgentUnauthorizedUserEntity;
import org.skyworth.ess.permissions.service.IAgentUnauthorizedUserService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.feign.IPlantClient;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.vo.PlantAgentViewVO;
import org.skyworth.ess.plant.vo.PlantClientVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class PlantClient implements IPlantClient {
	private IPlantService plantService;
	private IAgentUnauthorizedUserService agentUnauthorizedUserService;

	@GetMapping(PLANT_BY_USER_INFO)
	@Override
	public R<List<PlantClientVO>> getPlantByUserInfo(PlantClientVO vo) {
		return plantService.getPlantByUserInfo(vo);
	}

	@Override
	@GetMapping(PLANT_AGENT_VIEW_INFO)
	@TenantIgnore
	public R<IPage<PlantAgentViewVO>> getPlantAgentViewInfo(Long deptId, Query query) {
		return R.data(plantService.getPlantAgentViewInfo(deptId, query));
	}

	@Override
	@GetMapping(PLANT_QUERY_UNAUTHORIZED_PERSONNEL)
	@TenantIgnore
	public R<List<Long>> queryUnauthorizedUser(Long plantId) {
		List<Long> userIds = new ArrayList<>();
		List<AgentUnauthorizedUserEntity> agentUnauthorizedUserEntityList = agentUnauthorizedUserService.list(Wrappers.<AgentUnauthorizedUserEntity>lambdaQuery().eq(AgentUnauthorizedUserEntity::getPlantId, plantId));
		if (CollectionUtils.isNotEmpty(agentUnauthorizedUserEntityList)) {
			userIds = agentUnauthorizedUserEntityList.stream().map(AgentUnauthorizedUserEntity::getUnauthorizedUserId).collect(Collectors.toList());
		}
		return R.data(userIds);
	}

	@Override
	@TenantIgnore
	@GetMapping(CLEAN_PLANT_OPERATION_USER_ID)
	public R<Boolean> cleanPlantOperationUserId(List<Long> userIdList) {
		return R.data(plantService.cleanPlantOperationUserId(userIdList));
	}

	@Override
	@TenantIgnore
	@PostMapping(CLEAN_PLANT_DEPT_ID_OR_USERS)
	public R<Boolean> cleanPlantDeptIdOrUsers(JSONObject jsonObject) {
		return R.data(plantService.cleanPlantDeptIdOrUsers(jsonObject));
	}

	@Override
	public R<Integer> updatePlant(JSONObject jsonObject) {
		PlantEntity plantEntity = new PlantEntity();
		Long plantId = jsonObject.getLong("plantId");
		if (plantId == null) {
			return R.fail("Plant id is not null");
		}
		plantEntity.setId(plantId);
		if (ObjectUtil.isNotEmpty(jsonObject.getDate("installDate"))) {
			plantEntity.setInstallDate(jsonObject.getDate("installDate"));
		}
		if (ObjectUtil.isNotEmpty(jsonObject.getLong("distributorId"))) {
			plantEntity.setInstallTeamId(jsonObject.getLong("distributorId"));
		}
		return R.data(plantService.updatePlant(plantEntity));
	}
}
