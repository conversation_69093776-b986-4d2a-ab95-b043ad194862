package org.skyworth.ess.fegin;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.device.client.ITimeZoneDeviceServiceClient;
import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.skyworth.ess.device.mapper.TimeZoneDeviceMapper;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年5月8日 14:23:30
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class TimeZoneDeviceServiceClient extends BaseServiceImpl<TimeZoneDeviceMapper, TimeZoneDevice> implements ITimeZoneDeviceServiceClient {
	@Autowired
	private TimeZoneDeviceService timeZoneDeviceService;
	@Autowired
	private IAppSetupService appSetupService;
	@Override
	@PostMapping(address)
	public List<TimeZoneDevice> getList(@RequestBody List<String> deviceSnList) {
		return timeZoneDeviceService.getListBySnList(deviceSnList);
	}

	@Override
	public List<TimeZoneDevice> getListByPlantIdList(List<Long> plantIdList) {
		return timeZoneDeviceService.getListByPlantIdList(plantIdList);
	}

	@Override
	public R adjustEquipmentTime(JSONObject jsonObject) {
		if (jsonObject == null) {
			return R.fail("参数错误");
		}
		AppAdvancedSetup deviceAdvancedSetup = JsonUtil.parse(jsonObject.toJSONString(), AppAdvancedSetup.class);
		return appSetupService.issueSetupByAutoAdjust(deviceAdvancedSetup);
	}

	@Override
	public R<Map<String, String>> getMapFromCacheByPlantIdList(List<Long> plantIdList) {
		return R.data(timeZoneDeviceService.getMapFromCacheByPlantIdList(plantIdList));
	}
}
