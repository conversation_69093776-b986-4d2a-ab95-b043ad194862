/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.excel.OtaUpdatePackExcel;
import org.skyworth.ess.ota.service.IOtaUpdatePackService;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.skyworth.ess.ota.wrapper.OtaUpdatePackWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * OTA升级包 控制器
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("/otaUpdatePack")
@Api(value = "OTA升级包", tags = "OTA升级包接口")
public class OtaUpdatePackController extends BladeController {

	private final IOtaUpdatePackService otaUpdatePackService;

	/**
	 * OTA升级包 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入otaUpdatePack")
	@PreAuth("hasPermission('client:otaUpdatePack:detail')")
	public R<OtaUpdatePackVO> detail(OtaUpdatePackEntity otaUpdatePack) {
		//
		OtaUpdatePackEntity detail = otaUpdatePackService.detail(otaUpdatePack);
		return R.data(OtaUpdatePackWrapper.build().entityVO(detail));
	}
	/**
	 * OTA升级包 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入otaUpdatePack")
	@PreAuth("hasPermission('client:otaUpdatePack:list')")
	public R<IPage<OtaUpdatePackVO>> list(@ApiIgnore @RequestParam Map<String, Object> otaUpdatePack, Query query) {
		IPage<OtaUpdatePackEntity> pages = otaUpdatePackService.pageList(Condition.getPage(query), Condition.getQueryWrapper(otaUpdatePack, OtaUpdatePackEntity.class).orderByDesc("company","update_time","id"));
		return R.data(OtaUpdatePackWrapper.build().pageVO(pages));
	}


	/**
	 * OTA升级包 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入otaUpdatePack")
	@PreAuth("hasPermission('client:otaUpdatePack:add')")
	public R save(@Valid @RequestBody OtaUpdatePackEntity otaUpdatePack) {
		return R.status(otaUpdatePackService.saveAndDealWithAttachment(otaUpdatePack));
	}

	/**
	 * OTA升级包 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入otaUpdatePack")
	@PreAuth("hasPermission('client:otaUpdatePack:update')")
	public R update(@Valid @RequestBody OtaUpdatePackEntity otaUpdatePack) {
		return R.status(otaUpdatePackService.updateAndDealWithAttachment(otaUpdatePack));
	}

	/**
	 * OTA升级包 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入otaUpdatePack")
	@PreAuth("hasPermission('client:otaUpdatePack:update')")
	public R submit(@Valid @RequestBody OtaUpdatePackEntity otaUpdatePack) {
		return R.status(otaUpdatePackService.saveOrUpdate(otaUpdatePack));
	}

	/**
	 * OTA升级包 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:otaUpdatePack:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(otaUpdatePackService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-otaUpdatePack")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入otaUpdatePack")
	@PreAuth("hasPermission('client:otaUpdatePack:export')")
	public void exportOtaUpdatePack(@ApiIgnore @RequestParam Map<String, Object> otaUpdatePack, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<OtaUpdatePackEntity> queryWrapper = Condition.getQueryWrapper(otaUpdatePack, OtaUpdatePackEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(OtaUpdatePack::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(OtaUpdatePackEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<OtaUpdatePackExcel> list = otaUpdatePackService.exportOtaUpdatePack(queryWrapper);
		ExcelUtil.export(response, "OTA升级包数据" + DateUtil.time(), "OTA升级包数据表", list, OtaUpdatePackExcel.class);
	}
}
