/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.excel.SoftwareUpgradeRecordExcel;
import org.skyworth.ess.ota.service.ISoftwareUpgradeRecordService;
import org.skyworth.ess.ota.vo.SoftwareUpgradeRecordVO;
import org.skyworth.ess.ota.wrapper.SoftwareUpgradeRecordWrapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备软件版本升级记录表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/softwareUpgradeRecord")
@Api(value = "设备软件版本升级记录表", tags = "设备软件版本升级记录表接口")
public class SoftwareUpgradeRecordController extends BladeController {

	private final ISoftwareUpgradeRecordService softwareUpgradeRecordService;
	private final IDictBizClient dictBizClient;

	/**
	 * 设备软件版本升级记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:detail')")
	public R<SoftwareUpgradeRecordVO> detail(SoftwareUpgradeRecordEntity softwareUpgradeRecord) {
		SoftwareUpgradeRecordEntity detail = softwareUpgradeRecordService.getOne(Condition.getQueryWrapper(softwareUpgradeRecord));
		return R.data(SoftwareUpgradeRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 设备软件版本升级记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:list')")
	public R<IPage<SoftwareUpgradeRecordVO>> list(@ApiIgnore @RequestParam Map<String, Object> softwareUpgradeRecord, Query query) {
		IPage<SoftwareUpgradeRecordEntity> pages = softwareUpgradeRecordService.selectSoftwareUpgradeRecord(softwareUpgradeRecord,query);
		return R.data(SoftwareUpgradeRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 设备软件版本升级记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:list')")
	public R<IPage<SoftwareUpgradeRecordVO>> page(SoftwareUpgradeRecordVO softwareUpgradeRecord, Query query) {
		IPage<SoftwareUpgradeRecordVO> pages = softwareUpgradeRecordService.selectSoftwareUpgradeRecordPage(Condition.getPage(query), softwareUpgradeRecord);
		return R.data(pages);
	}

	/**
	 * 设备软件版本升级记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:add')")
	public R save(@Valid @RequestBody SoftwareUpgradeRecordEntity softwareUpgradeRecord) {
		return R.status(softwareUpgradeRecordService.save(softwareUpgradeRecord));
	}

	/**
	 * 设备软件版本升级记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:update')")
	public R update(@Valid @RequestBody SoftwareUpgradeRecordEntity softwareUpgradeRecord) {
		return R.status(softwareUpgradeRecordService.updateById(softwareUpgradeRecord));
	}

	/**
	 * 设备软件版本升级记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:update')")
	public R submit(@Valid @RequestBody SoftwareUpgradeRecordEntity softwareUpgradeRecord) {
		return R.status(softwareUpgradeRecordService.saveOrUpdate(softwareUpgradeRecord));
	}

	/**
	 * 设备软件版本升级记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(softwareUpgradeRecordService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-softwareUpgradeRecord")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入softwareUpgradeRecord")
	@PreAuth("hasPermission('client:softwareUpgradeRecord:export')")
	public void exportSoftwareUpgradeRecord(@ApiIgnore @RequestParam Map<String, Object> softwareUpgradeRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SoftwareUpgradeRecordEntity> queryWrapper = Condition.getQueryWrapper(softwareUpgradeRecord, SoftwareUpgradeRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SoftwareUpgradeRecord::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(SoftwareUpgradeRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SoftwareUpgradeRecordExcel> list = softwareUpgradeRecordService.exportSoftwareUpgradeRecord(queryWrapper);
		ExcelUtil.export(response, "设备软件版本升级记录表数据" + DateUtil.time(), "设备软件版本升级记录表数据表", list, SoftwareUpgradeRecordExcel.class);
	}

}
