/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.feign;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ota.service.ISoftwareUpgradeRecordService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 *  Feign实现类
 *
 * <AUTHOR> @since
 */
@ApiIgnore()
@RestController
@AllArgsConstructor
public class DeviceSoftwareUpgradeRecordClient implements IDeviceSoftwareUpgradeRecordClient {

	private final ISoftwareUpgradeRecordService softwareUpgradeRecordService;

	@Override
	@PostMapping(GET_BY_ID)
	public JSONObject getById(Long id) {
		return softwareUpgradeRecordService.getByIdFeign(id);
	}
}
