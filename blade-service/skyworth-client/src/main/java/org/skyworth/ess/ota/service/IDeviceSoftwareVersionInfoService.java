/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.ota.entity.DeviceSoftwareVersionInfoEntity;
import org.skyworth.ess.ota.excel.DeviceSoftwareVersionInfoExcel;
import org.skyworth.ess.ota.vo.DeviceSoftwareVersionInfoVO;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 设备软件版本信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface IDeviceSoftwareVersionInfoService extends BaseService<DeviceSoftwareVersionInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceSoftwareVersionInfo
	 * @return
	 */
	IPage<DeviceSoftwareVersionInfoVO> selectDeviceSoftwareVersionInfoPage(IPage<DeviceSoftwareVersionInfoVO> page, DeviceSoftwareVersionInfoVO deviceSoftwareVersionInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceSoftwareVersionInfoExcel> exportDeviceSoftwareVersionInfo(Wrapper<DeviceSoftwareVersionInfoEntity> queryWrapper);

	/**
	 * 循环处理升级记录
	 *
	 * <AUTHOR>
	 * @since 2023/9/21 19:04
	 **/
	void circularProcessingUpgradeRecords();

	/**
	 * 获取OTA最终升级结果
	 *
	 * @param map 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:55
	 **/
	void obtainUpgradeResults(Map<String, String> map);

	/**
	 * 获取OTA软件下发结果
	 *
	 * @param deviceSns 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:55
	 **/
	void obtainUpgradePushResults(List<String> deviceSns);


	/**
	 * 对长时间未响应的下发记录，以及未返回结果的升级数据，调整状态为 升级失败
	 *
	 * <AUTHOR>
	 * @since 2023/9/25 13:25
	 **/
	void processingTimeoutStatus();

	/**
	 * 修改升级表状态，以及当前升级版本
	 *
	 * @param otaUpdatePackVOList 入参
	 * @return void
	 * <AUTHOR>
	 * @since 2023/9/25 14:32
	 **/
	void updateStatusAndLatestReleasedVersion(List<OtaUpdatePackVO> otaUpdatePackVOList);

	/**
	 * 手动重试
	 *
	 * @param deviceSoftwareVersionIds 入参
	 * <AUTHOR>
	 * @since 2023/9/25 15:05
	 **/
	void retry(List<Long> deviceSoftwareVersionIds);

	/**
	 * 查询设备当前版本是否是最新版+是否存在ota升级包
	 *
	 * @param ids 入参
	 * @return List<String>
	 * <AUTHOR>
	 * @since 2023/12/7 11:16
	 **/
	Set<Long> validDeviceIsLatestVersion(List<Long> ids);

	R<IPage<DeviceSoftwareVersionInfoVO>> queryList(Map<String, Object> deviceSoftwareVersionInfo, Query query);

	/**
	 * 告知app ota升级结果
	 *
	 * @param map
	 */
	void publishOtaUpgradeResultToApp(Map<String, Object> map);

	/**
	 * 脱机上报异常
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/3/5 17:48
	 **/
	void otaOfflineUpgradeException(JSONObject jsonObject);

	void batchDeleteByDeviceSn(List<String> listDeviceSn);
}
