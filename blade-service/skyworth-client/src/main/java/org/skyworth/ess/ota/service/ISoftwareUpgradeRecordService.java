/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.excel.SoftwareUpgradeRecordExcel;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.skyworth.ess.ota.vo.SoftwareUpgradeRecordVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import java.util.List;
import java.util.Map;

/**
 * 设备软件版本升级记录表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
public interface ISoftwareUpgradeRecordService extends BaseService<SoftwareUpgradeRecordEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param softwareUpgradeRecord
	 * @return
	 */
	IPage<SoftwareUpgradeRecordVO> selectSoftwareUpgradeRecordPage(IPage<SoftwareUpgradeRecordVO> page, SoftwareUpgradeRecordVO softwareUpgradeRecord);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SoftwareUpgradeRecordExcel> exportSoftwareUpgradeRecord(Wrapper<SoftwareUpgradeRecordEntity> queryWrapper);

	/**
	 * 批量插入记录表
	 * @param otaUpdatePackVOList 入参
	 * @return void
	 * <AUTHOR>
	 * @since 2023/9/25 9:40
	 **/
	void batchInsert(List<OtaUpdatePackVO> otaUpdatePackVOList);

	JSONObject getByIdFeign(Long id);

    IPage<SoftwareUpgradeRecordEntity> selectSoftwareUpgradeRecord(Map<String, Object> softwareUpgradeRecord, Query query);
}
