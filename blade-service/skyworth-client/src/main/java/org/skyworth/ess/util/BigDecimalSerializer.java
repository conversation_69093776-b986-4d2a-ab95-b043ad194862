package org.skyworth.ess.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * 精度去除多余的0
 * */
public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
		if(ObjectUtils.isNotEmpty(value)){
			BigDecimal strippedValue = value.stripTrailingZeros();
			gen.writeString(strippedValue.toPlainString());
		}
    }
}
