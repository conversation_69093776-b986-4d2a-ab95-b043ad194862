package org.skyworth.ess.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * BigDecimal，如果数据为0时，不保留任何小数，否则保留2位小数
 * */
public class BigDecimalSerializer2Scale extends JsonSerializer<BigDecimal> {
    @Override
	public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
		if (value != null) {
			BigDecimal scaledValue;
			if (value.compareTo(BigDecimal.ZERO) == 0) {
				scaledValue = BigDecimal.ZERO;
			} else {
				scaledValue = value.setScale(2, RoundingMode.HALF_UP);
			}
			gen.writeString(scaledValue.toPlainString());
		}
	}
}
