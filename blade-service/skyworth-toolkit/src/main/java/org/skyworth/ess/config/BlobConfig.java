/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-27 14:04
 **/
@Configuration
@Data
@NoArgsConstructor
public class BlobConfig {
	@Value("${azure-blob.defaultEndpointsProtocol:}")
	private String defaultEndpointsProtocol;
	@Value("${azure-blob.accountName:}")
	private String accountName;
	@Value("${azure-blob.accountKey:}")
	private String accountKey;
	@Value("${azure-blob.endpointSuffix:}")
	private String endpointSuffix;
	@Value("${azure-blob.blobEndpoint:}")
	private String blobEndpoint;
	@Value("${azure-blob.queueEndpoint:}")
	private String queueEndpoint;
	@Value("${azure-blob.tableEndpoint:}")
	private String tableEndpoint;
}
