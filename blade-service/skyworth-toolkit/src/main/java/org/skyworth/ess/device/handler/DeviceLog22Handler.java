package org.skyworth.ess.device.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.skyworth.ess.device.BinaryConvertUtils2;
import org.skyworth.ess.device.DeviceLogService;
import org.skyworth.ess.device.client.IDeviceLog22Client;
import org.skyworth.ess.device.client.IErrorInfoClient;
import org.skyworth.ess.device.client.ITimeZoneDeviceServiceClient;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.skyworth.ess.log.service.OriginalDataRecordService;
import org.skyworth.ess.sink.DorisSinkService;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 处理所上报2.2的数据
 */
@Component("DeviceLog2_" + Constants.SECOND)
@Slf4j
@RequiredArgsConstructor
public class DeviceLog22Handler implements DeviceLogService {
	@Autowired
	private BinaryConvertUtils2 binaryConvertUtils;
	@Autowired
	private DorisSinkService dorisSinkService;
	@Autowired
	private OriginalDataRecordService recordService;
	@Autowired
	private IErrorInfoClient errorInfoClient;
	@Autowired
	private BladeRedis bladeRedis;
	@Autowired
	private ITimeZoneDeviceServiceClient timeZoneDeviceServiceClient;
	@Autowired
	private IDeviceLog22Client deviceLog22Client;
	@Autowired
	private IDictBizClient dictBizClient;
	@Value("${blade.switch.reserve}")
	private String switchFlag;

	private static final String DEVICE_TIME_ADJUSTMENT = "device_time_adjustment_";

	private final Lock redisLock = new ReentrantLock();

	// 当天上报记录的前一条记录的key
	private static final String DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY = "deviceLog22TodayBeforeRecordKey:";

	// 历史记录的key，用于累加汇总数据  accumulatedEnergyOfPositive
	private static final String DEVICE_LOG22_HISTORY_RECORD_KEY = "deviceLog22HistoryRecordKey:";

	private static final BigDecimal FIVE_MINUTES = new BigDecimal("12");

	private static BigDecimal THOUSAND_KWH = new BigDecimal("1000");
	/**
	 * 处理业务逻辑
	 */
	@Override
	public void handler(List<JSONObject> dataList) {
		List<JSONObject> jsonObjects = new ArrayList<>();
		//预留数据
		List<JSONObject> jsonObjectsRes=new ArrayList<>();
		List<Long> plantIdList = dataList.stream()
			.filter(jsonObject -> jsonObject.containsKey("plantID"))
			.map(jsonObject -> Long.parseLong(jsonObject.getString("plantID")))
			.collect(Collectors.toList());
		List<Map<String,Object>> plantMapList=recordService.isParallelModeList(plantIdList);
		Map<Long, String> resultMap = plantMapList.stream()
			.filter(map -> map.containsKey("plantId") && map.containsKey("modeType"))
			.collect(Collectors.toMap(
				map -> (Long) map.get("plantId"),
				map -> (String) map.get("modeType")
			));
		dataList.stream().parallel().forEach(data -> {
			try {
				Long plantId = Long.parseLong(data.getString("plantID"));
				String modbusProtocolVersion = data.getString("commuVersion");
				String deviceSerialNumber = data.getString("deviceSn");
				String content = data.getString("content");
				log.info("DeviceLog22Handler timeStamp {} :", data.getLong("timeStamp") * 1000);
				Long timeStamp = data.getLong("timeStamp");
				String timeZone = data.getString("timeZone");
				Date deviceDateTime = DateUtil.convertLongTimeZoneForDeviceUpload(timeStamp,timeZone,CommonConstant.COMMON_DEFAULT_TIME_ZONE);
				log.info("DeviceLog22Handler deviceDateTime {} :", deviceDateTime);
				JSONObject tmp = binaryConvertUtils.getData(content, Constants.SECOND, modbusProtocolVersion);
				log.info("DeviceLog22Handler getData {} :", tmp);
				if (tmp != null) {
					DeviceLog22 deviceLog22 = JSON.toJavaObject(tmp, DeviceLog22.class);   //将多余的字段去除
					JSONObject result = (JSONObject) JSON.toJSON(deviceLog22);
					JSONObject newObj = convertKeysToUnderscore(result); //将驼峰转化为下划线
					String errorMessage4 = newObj.getString("error_message4");
					if (StringUtils.isNotEmpty(errorMessage4)) {
						newObj.put("error_message_4", errorMessage4);
						newObj.remove("error_message4");
					}
					IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
					Number number = identifierGenerator.nextId(new Object());
					newObj.put("id", number.longValue());
					newObj.put("modbus_protocol_version", modbusProtocolVersion);
					newObj.put("device_serial_number", deviceSerialNumber);
					newObj.put("device_date_time", DateUtil.parseDateToString(deviceDateTime,DateUtil.PATTERN_DATETIME));
					newObj.put("plant_id", plantId);
					String currentTime = TimeUtils.getCurrentTime();
					newObj.put("create_time", currentTime);
					newObj.put("update_time", currentTime);
					String modeType=resultMap.get(plantId);
					newObj.put("is_parallel_mode",modeType);
					//非并机数据
					if(ValidationUtil.isNotEmpty(modeType)&&!Constants.ONE.equals(modeType)){
						JSONObject notParallel=JSONObject.parseObject(newObj.toJSONString());
						//判断开关是否打开
						if(ValidationUtil.isNotEmpty(switchFlag)&&Constants.ONE.equals(switchFlag)){
							jsonObjectsRes.add(notParallel);
						}
						// 非并机时上传了并机数据
						processNonParallel(newObj);
					}
					jsonObjects.add(newObj);
				}
			} catch (Exception e) {
				log.error(e.getMessage());
				//将数据插入记录表中  保留异常信息
				OriginalDataRecord record = new OriginalDataRecord();
				record.setData(data.toJSONString());
				record.setType("1");
				record.setExceptionInfo(e.getMessage());
				recordService.save(record);
			}
		});
		log.info("DeviceLog22Handler : {} ", jsonObjects);
		log.info("DeviceLog22Handler new info ");
		if (!jsonObjects.isEmpty()) {
//			this.calFromToGridAndAccumulatedEnergy(jsonObjects);
			dorisSinkService.write(jsonObjects, "device_log22");
		}
		if(!jsonObjectsRes.isEmpty()){
			dorisSinkService.write(jsonObjectsRes,"device_log22_res");
		}
		// 解决逆变器上报时间不正确问题
		ThreadPoolCustom.getCustomThreadPool().submit(() -> checkEquipmentTime(dataList));
	}

	private void calFromToGridAndAccumulatedEnergy(List<JSONObject> jsonObjects) {
		// 获取开关
		boolean fromToGridSwitch = this.getFromToGridSwitch();
		log.info("calFromToGridAndAccumulatedEnergy -> fromToGridSwitch : {} ", fromToGridSwitch);
		if(fromToGridSwitch) {
			// 组装查询条件，1、截止到上报时间点之前最后一条数据  2、from grid的累计相加的值，取站点id和设备sn下最大的记录值
			Pair<List<DeviceLog22>, List<DeviceLog22>> queryConditionPair = this.assembleQueryCondition(jsonObjects);
			// 第一次查询数据库，如果db中不存在，则表示当天还未上报数据，则设置到redis中默认为0，再下一步中再将 此次上报的数据累加到redis中
			this.queryDbDataAndSetToRedis(queryConditionPair);
			for(JSONObject currentDeviceData2DbJson : jsonObjects) {
				String deviceSerialNumber = (String) currentDeviceData2DbJson.get("device_serial_number");
				Long plantId = (Long) currentDeviceData2DbJson.get("plant_id");
				this.fromToGridCurrentDeviceDataAddHistory2Db(currentDeviceData2DbJson, deviceSerialNumber, plantId);
				this.accumulatedEnergyCurrentDeviceDataAddHistory2Db(currentDeviceData2DbJson, deviceSerialNumber, plantId);
				log.info("calFromToGridAndAccumulatedEnergy -> deviceData : {} ", jsonObjects);
			}

		}
	}

	private void accumulatedEnergyCurrentDeviceDataAddHistory2Db(JSONObject currentDeviceData2DbJson, String deviceSerialNumber, Long plantId) {
		JSONObject redisTotalJsonObject = (JSONObject) bladeRedis.get(DEVICE_LOG22_HISTORY_RECORD_KEY + deviceSerialNumber + ":" + plantId);
		BigDecimal DeviceAccumulatedEnergyOfPositive = (BigDecimal) currentDeviceData2DbJson.get("accumulated_energy_of_positive");
		// 此处不可能为空，因为在 查询db之后，如果db中没有，默认赋值为0
		if(redisTotalJsonObject != null && !redisTotalJsonObject.isEmpty()) {
			BigDecimal historyAccumulatedEnergyOfPositive = (BigDecimal) redisTotalJsonObject.get("accumulated_energy_of_positive");
			BigDecimal addAccumulatedEnergyOfPositive = DeviceAccumulatedEnergyOfPositive.add(historyAccumulatedEnergyOfPositive);
			JSONObject value = new JSONObject();
			value.put("accumulated_energy_of_positive",addAccumulatedEnergyOfPositive);
			bladeRedis.set(DEVICE_LOG22_HISTORY_RECORD_KEY + deviceSerialNumber + ":" + plantId, value);
			currentDeviceData2DbJson.put("accumulated_energy_of_positive",addAccumulatedEnergyOfPositive);
			log.info("calFromToGridAndAccumulatedEnergy -> accumulatedEnergyCurrentDeviceDataAddHistory2Db -> fromToGridRedis : {} ", value);
		}
//					else {
//						BigDecimal addAccumulatedEnergyOfPositive = DeviceAccumulatedEnergyOfPositive.add(new BigDecimal("0"));
//						JSONObject value = new JSONObject();
//						value.put("accumulated_energy_of_positive",addAccumulatedEnergyOfPositive);
//						bladeRedis.set(DEVICE_LOG22_HISTORY_RECORD_KEY + deviceSerialNumber + ":" + plantId, value);
//						currentDeviceData2DbJson.put("accumulated_energy_of_positive",addAccumulatedEnergyOfPositive);
//					}
	}

	private void fromToGridCurrentDeviceDataAddHistory2Db(JSONObject currentDeviceData2DbJson, String deviceSerialNumber, Long plantId) {
		String deviceDateTime = (String) currentDeviceData2DbJson.get("device_date_time");
		// 获取年月日格式
		String yearMothDay = deviceDateTime.substring(0, 10);
		JSONObject redisCurrentDayJsonObject = (JSONObject) bladeRedis.get(DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY + deviceSerialNumber + ":" + plantId + ":" + yearMothDay);
		// l1
		BigDecimal phaseRWattOfGrid = (BigDecimal) currentDeviceData2DbJson.get("phase_r_watt_of_grid");
		// l2
		BigDecimal phaseSWattOfGrid = (BigDecimal) currentDeviceData2DbJson.get("phase_s_watt_of_grid");
		// l3
		BigDecimal phaseTWattOfGrid = (BigDecimal) currentDeviceData2DbJson.get("phase_t_watt_of_grid");

		BigDecimal currentDeviceAddResultPtotw = phaseRWattOfGrid.add(phaseSWattOfGrid).add(phaseTWattOfGrid);
		// 此处不可能为空，因为在 查询db之后，如果db中没有，默认赋值为0
		if(redisCurrentDayJsonObject != null && !redisCurrentDayJsonObject.isEmpty()) {
			// from grid
			BigDecimal redisTodayImportEnergyTotal = (BigDecimal) redisCurrentDayJsonObject.get("today_import_energy");
			// to grid
			BigDecimal redisTodayExportEnergyTotal = (BigDecimal) redisCurrentDayJsonObject.get("today_export_energy");
			// 如果缓存中存在，则取历史 的 值 相加
			this.calFromToGridToDb(currentDeviceAddResultPtotw,redisTodayImportEnergyTotal, redisTodayExportEnergyTotal, deviceSerialNumber, plantId,yearMothDay, currentDeviceData2DbJson);
		}
//					else {
//						this.calFromToGridToDb(currentDeviceAddResultPtotw,new BigDecimal("0"), new BigDecimal("0"), deviceSerialNumber,plantId,yearMothDay,currentDeviceData2DbJson);
//					}
	}

	private void queryDbDataAndSetToRedis(Pair<List<DeviceLog22>, List<DeviceLog22>> queryConditionPair) {
		List<DeviceLog22> queryCurrentDayDeviceLog22List = queryConditionPair.getLeft();
		List<DeviceLog22> queryHistoryDeviceLog22List = queryConditionPair.getRight();
		if(!CollectionUtils.isNullOrEmpty(queryCurrentDayDeviceLog22List)) {
			List<DeviceLog22> dbCurrentDayDeviceLog22List = deviceLog22Client.queryDeviceLog22list(queryCurrentDayDeviceLog22List);
			for(DeviceLog22 deviceLog22 : dbCurrentDayDeviceLog22List) {
				String currentYearMothDay = DateUtil.parseDateToString(deviceLog22.getDeviceDateTime(), DateUtil.PATTERN_DATE);
				JSONObject value = new JSONObject();
				value.put("today_import_energy", deviceLog22.getTodayImportEnergy() == null ? BigDecimal.ZERO : deviceLog22.getTodayImportEnergy());
				value.put("today_export_energy", deviceLog22.getTodayExportEnergy() == null ? BigDecimal.ZERO : deviceLog22.getTodayExportEnergy());
				log.info("calFromToGridAndAccumulatedEnergy -> queryDbDataAndSetToRedis -> fromToGrid : {} ", value);
				bladeRedis.setEx(DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY + deviceLog22.getDeviceSerialNumber() + ":" + deviceLog22.getPlantId() + ":" + currentYearMothDay,value,Duration.ofDays(2));
			}
		}
		if(!CollectionUtils.isNullOrEmpty(queryHistoryDeviceLog22List)) {
			List<DeviceLog22> dbHistoryDeviceLog22List = deviceLog22Client.queryDeviceLog22list(queryHistoryDeviceLog22List);
			for(DeviceLog22 deviceLog22 : dbHistoryDeviceLog22List) {
				JSONObject value = new JSONObject();
				value.put("accumulated_energy_of_positive", deviceLog22.getAccumulatedEnergyOfPositive() == null ? BigDecimal.ZERO : deviceLog22.getAccumulatedEnergyOfPositive());
				log.info("calFromToGridAndAccumulatedEnergy -> queryDbDataAndSetToRedis -> accumulatedEnergy : {} ", value);
				// 不需要过期
				bladeRedis.set(DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY + deviceLog22.getDeviceSerialNumber() + ":" + deviceLog22.getPlantId() ,value);
			}
		}
	}

	private Pair<List<DeviceLog22>,List<DeviceLog22>> assembleQueryCondition(List<JSONObject> jsonObjects) {
		List<DeviceLog22> queryCurrentDayDeviceLog22List = new ArrayList<>();
		List<DeviceLog22> queryHistoryDeviceLog22List = new ArrayList<>();
		// 格式化 Date 对象并返回结果
		for(JSONObject jsonObject : jsonObjects) {
			String deviceSerialNumber = (String) jsonObject.get("device_serial_number");
			Long plantId = (Long) jsonObject.get("plant_id");
			String deviceDateTime = (String) jsonObject.get("device_date_time");

			JSONObject redisTotalJsonObject = (JSONObject) bladeRedis.get(DEVICE_LOG22_HISTORY_RECORD_KEY + deviceSerialNumber + ":" + plantId );
			log.info("calFromToGridAndAccumulatedEnergy -> assembleQueryCondition -> redisTotalJsonObject : {} ", redisTotalJsonObject);
			if(redisTotalJsonObject == null || redisTotalJsonObject.isEmpty()) {
				// 查询历史最大的记录，计算 accumulatedEnergyOfPositive
				DeviceLog22 queryHistoryDeviceLog22 = new DeviceLog22();
				queryHistoryDeviceLog22.setDeviceSerialNumber(deviceSerialNumber);
				queryHistoryDeviceLog22.setPlantId(plantId);
				queryHistoryDeviceLog22List.add(queryHistoryDeviceLog22);
			}

			// 获取年月日格式
			String yearMothDay = deviceDateTime.substring(0, 10);
			JSONObject redisCurrentDayJsonObject = (JSONObject) bladeRedis.get(DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY + deviceSerialNumber + ":" + plantId + ":" + yearMothDay);
			log.info("calFromToGridAndAccumulatedEnergy -> assembleQueryCondition -> redisCurrentDayJsonObject : {} ", redisCurrentDayJsonObject);
			if(redisCurrentDayJsonObject == null || redisCurrentDayJsonObject.isEmpty()) {
				// 查询当天的记录
				DeviceLog22 queryCurrentDayDeviceLog22 = new DeviceLog22();
				queryCurrentDayDeviceLog22.setDeviceSerialNumber(deviceSerialNumber);
				queryCurrentDayDeviceLog22.setPlantId(plantId);
				try {
					queryCurrentDayDeviceLog22.setDeviceDateTime(DateUtil.parseStringToDate(deviceDateTime, DateUtil.PATTERN_DATETIME));
				} catch (ParseException e) {
					log.error("DeviceLog22Handler parseStringToDate error : {} ", e.getMessage());
					continue;
				}
				queryCurrentDayDeviceLog22List.add(queryCurrentDayDeviceLog22);
			}

		}
		return Pair.of(queryCurrentDayDeviceLog22List,queryHistoryDeviceLog22List);
	}

	private void calFromToGridToDb(BigDecimal currentDeviceAddResultPtotw ,BigDecimal redisTodayImportEnergyTotal, BigDecimal redisTodayExportEnergyTotal,
								   String deviceSerialNumber,Long plantId,String yearMothDay,JSONObject currentDeviceData2DbJson) {
		log.info("calFromToGridAndAccumulatedEnergy -> calFromToGridToDb -> currentDeviceAddResultPtotw : {} ", currentDeviceAddResultPtotw);
		// 如果结果为正，则记为从电网耗电功率(单位W)
		if(currentDeviceAddResultPtotw.compareTo(BigDecimal.ZERO) > 0) {
			// E总=P总 /12 /1000  (说明:5分钟=1/12小时，除以1000将单位转为kWh) ,（此处不除以1000），在展示时在计算
			BigDecimal etotKWh = currentDeviceAddResultPtotw.divide(FIVE_MINUTES,RoundingMode.HALF_UP)//.divide(THOUSAND_KWH,RoundingMode.HALF_UP)
				.setScale(3, RoundingMode.HALF_UP);
			// 每个时刻的累积电量，为上一时刻的累计电量与当前时刻的总电量之和
			BigDecimal fromGridOrEcum = etotKWh.add(redisTodayImportEnergyTotal);
			JSONObject redisValue = new JSONObject();
			redisValue.put("today_import_energy", fromGridOrEcum);
			redisValue.put("today_export_energy", redisTodayExportEnergyTotal);
			bladeRedis.setEx(DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY + deviceSerialNumber + ":" + plantId + ":" + yearMothDay,redisValue,Duration.ofDays(2));
			log.info("calFromToGridAndAccumulatedEnergy -> calFromToGridToDb -> fromToGridRedis : {} ", redisValue);
			currentDeviceData2DbJson.put("today_import_energy", fromGridOrEcum);
			currentDeviceData2DbJson.put("today_export_energy", redisTodayExportEnergyTotal);
		} else {
			// 如果结果为负，则记为向电网馈电功率(单位W)
			// E总=P总 /12 /1000  (说明:5分钟=1/12小时，除以1000将单位转为kWh) ,（此处不除以1000），在展示时在计算
			BigDecimal etotKWh = currentDeviceAddResultPtotw.abs().divide(FIVE_MINUTES,RoundingMode.HALF_UP)//.divide(THOUSAND_KWH,RoundingMode.HALF_UP)
				.setScale(3, RoundingMode.HALF_UP);
			// 每个时刻的累积电量，为上一时刻的累计电量与当前时刻的总电量之和
			BigDecimal toGridOrEcum = etotKWh.add(redisTodayExportEnergyTotal);
			JSONObject redisValue = new JSONObject();
			redisValue.put("today_export_energy", toGridOrEcum);
			redisValue.put("today_import_energy", redisTodayImportEnergyTotal);
			bladeRedis.setEx(DEVICE_LOG22_TODAY_BEFORE_RECORD_KEY + deviceSerialNumber + ":" + plantId + ":" + yearMothDay,redisValue,Duration.ofDays(2));
			log.info("calFromToGridAndAccumulatedEnergy -> calFromToGridToDb -> fromToGridRedis : {} ", redisValue);
			currentDeviceData2DbJson.put("today_export_energy", toGridOrEcum);
			currentDeviceData2DbJson.put("today_import_energy", redisTodayImportEnergyTotal);
		}
	}
	private boolean getFromToGridSwitch() {
		R<List<DictBiz>> fromToGridSwitchResult = dictBizClient.getList(DictBizCodeEnum.CONTROL_FROM_TO_GRID_SWITCH.getDictCode());
		List<DictBiz> dictBizList = fromToGridSwitchResult.getData();
		if(CollectionUtils.isNullOrEmpty(dictBizList)) {
			return false;
		}
		if("Y".equalsIgnoreCase(dictBizList.get(0).getAttribute1())) {
			return true;
		}
		return false;
	}
	public void updateDeviceStatus(String errorCode, String deviceSerialNumber, Date deviceDateTime, Map<String,
		Map<String, Object>> errorResult,
								   String errorCode2, String errorCode3, String errorMessage4, String batteryCurrent,
								   Long plantId, String timeZone) {
		if (errorCode != null && StringUtils.isNotEmpty(errorCode)) {
			Map<String, Object> errorCodeData = new HashMap<>(8);
			errorCodeData.put("exceptionMessage", errorCode);
			errorCodeData.put("deviceSn", deviceSerialNumber);
			errorCodeData.put("deviceDateTime", deviceDateTime);
			errorCodeData.put("type", BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
			errorCodeData.put("plantId", plantId);
			errorCodeData.put("timeZone", timeZone);
			errorResult.put("101E", errorCodeData);
		}
		if (errorCode2 != null && StringUtils.isNotEmpty(errorCode2)) {
			Map<String, Object> errorCode2Data = new HashMap<>(8);
			errorCode2Data.put("exceptionMessage", errorCode2);
			errorCode2Data.put("deviceSn", deviceSerialNumber);
			errorCode2Data.put("deviceDateTime", deviceDateTime);
			errorCode2Data.put("type", BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
			errorCode2Data.put("plantId", plantId);
			errorCode2Data.put("timeZone", timeZone);
			errorResult.put("101F", errorCode2Data);
		}
		if (errorCode3 != null && StringUtils.isNotEmpty(errorCode3)) {
			Map<String, Object> errorCode3Data = new HashMap<>(8);
			errorCode3Data.put("exceptionMessage", errorCode3);
			errorCode3Data.put("deviceSn", deviceSerialNumber);
			errorCode3Data.put("deviceDateTime", deviceDateTime);
			errorCode3Data.put("type", BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
			errorCode3Data.put("plantId", plantId);
			errorCode3Data.put("timeZone", timeZone);
			errorResult.put("1020", errorCode3Data);
		}
		if (errorMessage4 != null && StringUtils.isNotEmpty(errorMessage4)) {
			Map<String, Object> errorCode4Data = new HashMap<>(8);
			errorCode4Data.put("exceptionMessage", errorMessage4);
			errorCode4Data.put("deviceSn", deviceSerialNumber);
			errorCode4Data.put("deviceDateTime", deviceDateTime);
			errorCode4Data.put("type", BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
			errorCode4Data.put("batteryCurrent", batteryCurrent);
			errorCode4Data.put("plantId", plantId);
			errorCode4Data.put("timeZone", timeZone);
			errorResult.put("2013", errorCode4Data);
		}
		errorInfoClient.errorInfo(errorResult);
	}

	private static JSONObject convertKeysToUnderscore(JSONObject jsonObject) {
		JSONObject convertedObject = new JSONObject();

		for (String key : jsonObject.keySet()) {
			String convertedKey = convertToUnderscore(key);
			Object value = jsonObject.get(key);
			if (value instanceof JSONObject) {
				value = convertKeysToUnderscore((JSONObject) value);
			}
			convertedObject.put(convertedKey, value);
		}
		return convertedObject;
	}

	private static String convertToUnderscore(String input) {
		StringBuilder result = new StringBuilder();
		boolean isFirstChar = true;

		for (char c : input.toCharArray()) {
			if (Character.isUpperCase(c)) {
				if (!isFirstChar) {
					result.append('_');
				}
				result.append(Character.toLowerCase(c));
			} else {
				result.append(c);
			}

			isFirstChar = false;
		}

		return result.toString();
	}

	/**
	 * 检查设备时间是否需要调整
	 * 遍历给定的设备信息列表，对于每个设备，如果其时间与当前时间相差超过10分钟，则将其添加到待调整列表中
	 * 如果待调整列表不为空，则更新这些设备的时间
	 *
	 * @param jsonObjectList 包含设备信息的JSON对象列表
	 */
	public void checkEquipmentTime(List<JSONObject> jsonObjectList) {
		log.info("checkEquipmentTime -> begin======================>");
		// 检查输入列表是否为空
		if (CollectionUtils.isNullOrEmpty(jsonObjectList)) {
			return;
		}
		long currentDateTimeLong = Instant.now().toEpochMilli();
		List<JSONObject> pendingJsonObjectList = new CopyOnWriteArrayList<>();
		for (JSONObject jsonObject : jsonObjectList) {
			if (!isValidJsonObject(jsonObject)) {
				continue;
			}
			try {
				processJsonObject(jsonObject, currentDateTimeLong, pendingJsonObjectList);
			} catch (JSONException e) {
				log.error("Invalid JSON object: {}", e.getMessage(), e);
			} catch (Exception e) {
				log.error("Error processing equipment time check: {}", e.getMessage(), e);
			}
		}
		if (!CollectionUtils.isNullOrEmpty(pendingJsonObjectList)) {
			log.info("isMoreThanTenMinutesApart -> updateDeviceTime======================>" );
			updateDeviceTime(pendingJsonObjectList, currentDateTimeLong);
		}
		log.info("checkEquipmentTime -> end======================>");
	}

	/**
	 * 处理JSON对象，根据时间差决定是否将设备数据添加到待处理列表中
	 * 此方法的主要目的是为了处理来自不同设备的JSON数据，并根据设备时间和当前时间的差异
	 * 来决定是否需要将数据添加到待处理数据列表中如果设备的时间与当前时间相差不超过十分钟，
	 * 则认为数据是及时的，否则数据将被加入到待处理列表中，以便后续处理
	 *
	 * @param jsonObject            设备数据的JSON对象，包含设备ID和时间戳
	 * @param currentDateTimeLong   当前的日期和时间，以毫秒为单位
	 * @param pendingJsonObjectList 待处理的JSON对象列表，如果设备数据不及时，将被添加到此列表中
	 * @throws Exception 如果在处理过程中遇到任何异常，将抛出此异常
	 */
	private void processJsonObject(JSONObject jsonObject, long currentDateTimeLong,
								   List<JSONObject> pendingJsonObjectList) throws Exception {
		// 获取设备ID
		String plantId = jsonObject.getString("plantID");
		// 获取时间戳
		Long timeStamp = jsonObject.getLong("timeStamp");
		// 构造Redis键名
		String redisKey = DEVICE_TIME_ADJUSTMENT + plantId;
		// 站点所属时区
		String timeZone = jsonObject.getString("timeZone");
		// 加锁以确保线程安全
		redisLock.lock();
		try {
			// 如果Redis中已存在该设备的记录，则直接返回，避免重复处理
			if (bladeRedis.exists(redisKey)) {
				return;
			}
			// 将设备时间戳根据时区转换为毫秒
			long deviceDateTimeLong = timeStamp * 1000;
			// 如果设备时间与当前时间相差超过十分钟，则将数据添加到待处理列表中
			if (isMoreThanTenMinutesApart(currentDateTimeLong, deviceDateTimeLong, timeZone)) {
				pendingJsonObjectList.add(jsonObject);
				// 在Redis中记录该设备，有效期为5分钟，以防止重复处理
				bladeRedis.setEx(redisKey, "1", Duration.ofMinutes(5));
			}
		} finally {
			// 解锁
			redisLock.unlock();
		}
	}

	/**
	 * 检查两个时间点是否相差超过10分钟
	 *
	 * @param currentDateTimeLong 当前时间
	 * @param deviceDateTimeLong  设备时间
	 * @param deviceTimeZone      设备所属时区
	 * @return 如果两个时间点相差超过10分钟，则返回true；否则返回false
	 */
	private boolean isMoreThanTenMinutesApart(long currentDateTimeLong, long deviceDateTimeLong,
											  String deviceTimeZone) {
		// 设备时区
		ZoneId deviceZoneId = ZoneId.of(deviceTimeZone);
		// 将 currentDateTimeLong 转换为目标时区的 LocalDateTime
		LocalDateTime currentDateTime = Instant.ofEpochMilli(currentDateTimeLong)
			.atZone(deviceZoneId)
			.toLocalDateTime();
		log.info("isMoreThanTenMinutesApart -> currentDateTime======================>" + currentDateTime);
		// 系统时区
		ZoneId systemZoneId = ZoneId.of(CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		// 将 deviceDateTimeLong 转换为目标时区的 LocalDateTime
		LocalDateTime deviceDateTime = Instant.ofEpochMilli(deviceDateTimeLong)
			.atZone(systemZoneId)
			.toLocalDateTime();
		log.info("isMoreThanTenMinutesApart -> deviceDateTime======================>" + deviceDateTime);
		// 计算两个时间之间的分钟差
		long minutesDifference = ChronoUnit.MINUTES.between(currentDateTime,deviceDateTime);
		log.info("isMoreThanTenMinutesApart -> minutesDifference======================>" + minutesDifference);
		return Math.abs(minutesDifference) >= 10;
	}


	/**
	 * 更新设备时间
	 * 遍历待调整的设备列表，为每个设备发送时间调整请求
	 *
	 * @param jsonObjectList      包含待调整设备信息的JSON对象列表
	 * @param currentDateTimeLong 当前时间
	 */
	private void updateDeviceTime(List<JSONObject> jsonObjectList, long currentDateTimeLong) {
		// 遍历待调整的设备列表
		for (JSONObject jsonObject : jsonObjectList) {
			try {
				long plantId = jsonObject.getLong("plantID");
				String deviceSn = jsonObject.getString("deviceSn");
				String timeZone = jsonObject.getString("timeZone");
				LocalDateTime deviceDateTimePush = DateUtil.convertLongTimeToLocalDateTime(currentDateTimeLong,
					CommonConstant.COMMON_DEFAULT_TIME_ZONE,
					timeZone);
				sendAdjustmentRequest(deviceSn, plantId, deviceDateTimePush, timeZone);
			} catch (Exception e) {
				log.error("Error processing device time adjustment: {}", e.getMessage(), e);
			}
		}
	}


	/**
	 * 发送设备时间调整请求
	 * 构建时间调整请求的JSON对象，并调用服务客户端发送请求
	 *
	 * @param deviceSn           设备序列号
	 * @param plantId            站点ID
	 * @param deviceDateTimePush 要设置的设备时间
	 */
	private void sendAdjustmentRequest(String deviceSn, long plantId, LocalDateTime deviceDateTimePush,
									   String timeZone) {
		JSONObject jsonObjectPush = new JSONObject();
		jsonObjectPush.put("deviceSerialNumber", deviceSn);
		jsonObjectPush.put("plantId", plantId);
		jsonObjectPush.put("issueSetupType", "advancedSetup");
		jsonObjectPush.put("timeZone", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		jsonObjectPush.put("issueSource", CommonConstant.AUTO_ADJUST);
		JSONArray jsonArray = new JSONArray();
		JSONObject jsonItemContent = new JSONObject();
		String dateTime = DateUtil.parseLocalDateTimeToStringDate(LocalDateTime.now(), DateUtil.PATTERN_DATETIME);
		jsonItemContent.put("definition", "dateTime");
		jsonItemContent.put("data", dateTime);
		jsonItemContent.put("definitionDesc", "Date and Time(From Backend)");
		jsonItemContent.put("dataDesc", dateTime);
		jsonArray.add(jsonItemContent);
		jsonObjectPush.put("setupItems", jsonArray);
		log.info("sendAdjustmentRequest -> jsonObjectPush======================>" + plantId);
		timeZoneDeviceServiceClient.adjustEquipmentTime(jsonObjectPush);
	}

	/**
	 * 验证 JSON 对象的有效性
	 *
	 * @param jsonObject JSON 对象
	 * @return 如果 JSON 对象有效，则返回 true；否则返回 false
	 */
	private boolean isValidJsonObject(JSONObject jsonObject) {
		try {
			jsonObject.getLong("plantID");
			jsonObject.getLong("timeStamp");
			jsonObject.getString("deviceSn");
			return true;
		} catch (JSONException e) {
			return false;
		}
	}

	/**
	 * 将非并机下的并机数据置0
	 * */
	private static void processNonParallel(JSONObject obj){
		obj.put("phase_l1_watt_of_grid_sum",0);
		obj.put("phase_l2_watt_of_grid_sum",0);
		obj.put("phase_l3_watt_of_grid_sum",0);
		obj.put("phase_l1_watt_of_load_sum",0);
		obj.put("phase_l2_watt_of_load_sum",0);
		obj.put("phase_l3_watt_of_load_sum",0);
		obj.put("daily_energy_of_load_sum",0);
		obj.put("monthly_energy_of_load_sum",0);
		obj.put("accumulated_energy_of_load_sum",0);
		obj.put("phase_l1_watt_sum_of_backup",0);
		obj.put("phase_l2_watt_sum_of_backup",0);
		obj.put("phase_l3_watt_sum_of_backup",0);
		obj.put("phase_l1_apparent_power_sum_of_backup",0);
		obj.put("phase_l2_apparent_power_sum_of_backup",0);
		obj.put("phase_l3_apparent_power_sum_of_backup",0);
		obj.put("daily_support_energy_sum_to_backup",0);
		obj.put("accumulated_support_energy_sum_to_backup",0);
		obj.put("phase_l1_watt_sum_of_generator",0);
		obj.put("phase_l2_watt_sum_of_generator",0);
		obj.put("phase_l3_watt_sum_of_generator",0);
		obj.put("phase_l1_apparent_power_sum_of_generator",0);
		obj.put("phase_l2_apparent_power_sum_of_generator",0);
		obj.put("phase_l3_apparent_power_sum_of_generator",0);
		obj.put("generator_today_energy_sum",0);
		obj.put("generator_total_energy_sum",0);
		obj.put("pvl_daily_generating_energy_sum",0);
		obj.put("pvl_accumulated_energy_sum",0);
		obj.put("totally_input_dc_watt_sum",0);
		obj.put("battery_power_sum",0);
		obj.put("battery_daily_charge_energy_parallel",0);
		obj.put("battery_accumulated_charge_energy_parallel",0);
		obj.put("battery_daily_discharge_energy_parallel",0);
		obj.put("battery_accumulated_discharge_energy_parallel",0);
	}
}
