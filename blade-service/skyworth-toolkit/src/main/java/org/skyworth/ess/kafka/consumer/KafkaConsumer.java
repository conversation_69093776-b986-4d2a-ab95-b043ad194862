package org.skyworth.ess.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.device.entity.Constants;
import org.springblade.common.eventCode.IotApplicationEvent;
import org.springblade.common.mq.kafka.config.KafkaAssignParallelPool;
import org.springblade.common.mq.kafka.config.KafkaDataHandler;
import org.springblade.core.tool.api.R;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@ConfigurationProperties("blade.kafka")
public class KafkaConsumer implements IotApplicationEvent {

	@Autowired
    BaseEnergyKafkaHandler baseEnergyKafkaHandler;

	@Autowired
	DistributionNetworkHandler distributionNetworkHandler;

	@Autowired
	ErrorInfoKafkaHandler errorInfoKafkaHandler;

	@Autowired
	WifiHeartKafkaHandler wifiHeartKafkaHandler;

	@Value("${blade.kafka.broker}")
	private String bootstrapServer;

	@Autowired
	IDictBizClient client;
    @Override
    public void onApplicationEvent() {
        Map<String, KafkaDataHandler> dataHandlerMap = new HashMap<>();
        dataHandlerMap.put(Constants.REAL_TOPIC, baseEnergyKafkaHandler);
		dataHandlerMap.put(Constants.SERVER_DISTRIBUTION_NETWORK, distributionNetworkHandler);
		dataHandlerMap.put(Constants.abnormal_data_server, errorInfoKafkaHandler);
		dataHandlerMap.put(Constants.HEART_BEAT_TOPIC, wifiHeartKafkaHandler);
        ArrayList<String> topics = new ArrayList<>();
        topics.add(Constants.REAL_TOPIC);
		topics.add(Constants.SERVER_DISTRIBUTION_NETWORK);
		topics.add(Constants.abnormal_data_server);
		topics.add(Constants.HEART_BEAT_TOPIC);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("max.poll.records", "50000");
        jsonObject.put("max.partition.fetch.bytes", 5 * 1024 * 1024 * 6);
		jsonObject.put("bootstrapServer",bootstrapServer);
        KafkaAssignParallelPool kafkaParallelPool = new KafkaAssignParallelPool(1, topics, dataHandlerMap, jsonObject);
        kafkaParallelPool.startHandler();
    }
}
