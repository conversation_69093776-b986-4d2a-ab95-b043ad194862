package org.skyworth.ess.log.service;

import com.alibaba.fastjson.JSONObject;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.log.entity.OriginalDataRecord;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

public interface OriginalDataRecordService extends BaseService<OriginalDataRecord> {

	Map<String,String> getTimeZone(String deviceSn);

	Map<String,Object> getStartupByBackstage(long plantId, String deviceSn);

	int updateWifiAndPlant(long plantId, String deviceSn);

	List<Map<String,Object>> isExistPlant(long plantId,String deviceSn);

	List<Map<String,Object>> isParallelModeList(List<Long> plantIds);
}
