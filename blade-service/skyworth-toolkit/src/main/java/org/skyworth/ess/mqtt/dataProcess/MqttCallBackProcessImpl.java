package org.skyworth.ess.mqtt.dataProcess;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.client.ICommonSetupServiceClient;
import org.skyworth.ess.device.client.IWifiStickPlantClient;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.kafka.producer.MsgProducer;
import org.skyworth.ess.ota.feign.IDeviceSoftwareUpgradeRecordClient;
import org.skyworth.ess.ota.feign.IDeviceSoftwareVersionInfoClient;
import org.skyworth.ess.threadpool.ThreadPoolCustom;
import org.skyworth.ess.timeUtil.TimeZoneInverter;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
@Slf4j
public class MqttCallBackProcessImpl implements MqttCallBackProcess {


	private MsgProducer msgProducer;

	private IWifiStickPlantClient wifiStickPlantClient;

	private BladeRedis bladeRedis;

	private IDeviceSoftwareVersionInfoClient deviceSoftwareVersionInfoClient;

	private IDeviceSoftwareUpgradeRecordClient deviceSoftwareUpgradeRecordClient;

	private ICommonSetupServiceClient commonSetupServiceClient;

	private static String NO_NEED_TO_UPGRADE = "The version is the same,upgrade fails.";

	@Override
	public void publish(String topic, String jsonObject) {
		msgProducer.sendMsg(Constants.REAL_TOPIC, jsonObject);
	}

	/**
	 * 配网成功转发至kafka
	 */
	@Override
	public void distributionNetwork(String topic, String jsonObject) {
		msgProducer.sendMsg(topic, jsonObject);
	}

	/**
	 * 异常事件转发kafka
	 */
	@Override
	public void errorInfoToKafka(String topic, String jsonObject) {
		msgProducer.sendMsg(topic, jsonObject);
	}

	@Override
	public void heartBeatWifiToKafka(String topic, String jsonObject) {
		msgProducer.sendMsg(topic, jsonObject);
	}


	@Override
	public void heartBeatWifi(String result) {

	}

	@Override
	public void subscribeMode(String result) {
		JSONObject content = JSONObject.parseObject(result);
		Integer code = content.getInteger("code");
		String requestId = content.getString("request_ID");
		if (1 == code) { //成功
			bladeRedis.setEx(requestId, "0", Duration.ofMinutes(5));
		} else { //失败
			bladeRedis.setEx(requestId, "1", Duration.ofMinutes(5));
		}
	}

	/**
	 * 订阅异常事件
	 */
	@Override
	public void subscribeErrorInfo(String result) {

	}


	/**
	 * 获取最终升级结果,更新状态为"升级成功/升级失败"
	 *
	 * @param result 入参
	 * <AUTHOR>
	 * @since 2023/9/22 15:06
	 **/
	@Override
	public void obtainUpgradeResults(String result) {
		JSONObject content = JSONObject.parseObject(result);
		String id = content.getString("requestId");
		bladeRedis.setEx(id, "1", Duration.ofSeconds(20));
		String code = content.getString("code");
		String message = content.getString("message");
		if (StringUtils.isAnyBlank(id, code, message)) {
			log.error("The parameter reported by the device is empty,id:" + (StringUtils.isBlank(id) ? "" : id));
			return;
		}

		// 如果这个requestId已经被平台用过了，则不再处理
		JSONObject softwareUpgradeRecord = deviceSoftwareUpgradeRecordClient.getById(Long.parseLong(id));
		if (ObjectUtil.isNotNull(softwareUpgradeRecord.get("upgradeCompletionTime"))) {
			log.warn("The ota upgrade result has been processed by the platform ,record id:{}", id);
			return;
		}

		Map<String, String> map = new HashMap<>(3);
		map.put("id", id);
		map.put("code", code);
		// 无须升级
		if (NO_NEED_TO_UPGRADE.equalsIgnoreCase(message)) {
			map.put("code", "-1");
		}
		map.put("message", message);
		deviceSoftwareVersionInfoClient.obtainUpgradeResults(map);
		Map<String, Object> appMap = new HashMap<>(3);
		appMap.put("id", id);
		appMap.put("code", Integer.parseInt(code));
		appMap.put("message", message);
		deviceSoftwareVersionInfoClient.publishOtaUpgradeResultToApp(appMap);
	}

	/**
	 * 获取升级包推送结果，更新状态为“升级中”
	 *
	 * @param deviceSns 入参
	 * <AUTHOR>
	 * @since 2023/9/22 16:26
	 **/
	@Override
	public void obtainUpgradePushResults(List<String> deviceSns) {
		deviceSoftwareVersionInfoClient.obtainUpgradePushResults(deviceSns);
	}

	/**
	 * 订阅设备上报结果
	 */
	@Override
	public void subscribeDeviceIssueRes(String result) {
		JSONObject content = JSONObject.parseObject(result);
		Integer code = content.getInteger("code");
		String requestId = content.getString("requestId");
		bladeRedis.hSet(requestId, "code", code);
		Integer address = content.getInteger("error_address");
		if (ValidationUtil.isNotEmpty(address)) {
			bladeRedis.hSet(requestId, "errorAddress", BinaryToHexUtils.decimalToHex(address));
		}

	}

	@Override
	public void subscribeDeviceTimeRes(String result) {
		JSONObject content = JSONObject.parseObject(result);
		String requestId = content.getString("requestId");
		String time = TimeZoneInverter.timeInverterToString("UTC-0", content.getLong("timeStamp") * 1000);
		bladeRedis.setEx(requestId, time, Duration.ofMinutes(1));

	}


	@Override
	public void subscribeWifiConnectedRes(String result) {
		JSONObject content = JSONObject.parseObject(result);
		String deviceSn = content.getString("deviceSn");
		bladeRedis.setEx(CommonConstant.PRE_FIX_FIRST_PLANT_DEVICE_RELATION_NET + "_" + deviceSn, "1", Duration.ofMinutes(2));
	}

	@Override
	public void otaOfflineUpgradeException(String result) {
		JSONObject content = JSONObject.parseObject(result);
		String bigType = content.getString("bigType");
		String smallType = content.getString("smallType");
		String deviceNumber = content.getString("deviceSn");
		if (StringUtils.isAnyBlank(bigType, smallType, deviceNumber)) {
			log.error(String.format("Offline upgrade exception message parameter is empty.deviceSn:%s;bigType:%s;smallType:%s;",
				StringUtils.isBlank(deviceNumber) ? "" : deviceNumber, StringUtils.isBlank(bigType) ? "" : bigType,
				StringUtils.isBlank(smallType) ? "" : smallType));
			return;
		}
		deviceSoftwareVersionInfoClient.otaOfflineUpgradeException(content);
	}

	@Override
	public void subscribeInverterGetServerTime(String result) {
		JSONObject content = JSON.parseObject(result);
		String deviceSn = content.getString("deviceSn");
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("deviceSn", deviceSn);
		// 解决逆变器上报时间不正确问题
		ThreadPoolCustom.getCustomThreadPool().submit(() -> getRes(jsonObject));
	}

	private R getRes(JSONObject jsonObject) {
		// 获取返回值
		R res = commonSetupServiceClient.invokeIssueInterface(jsonObject);
		if (!res.isSuccess()) {
			log.error("subscribeInverterGetServerTime occur error [deviceSn:{}] ==> {}", jsonObject.getString("deviceSn"), res.getMsg());
		}
		return res;
	}

}
