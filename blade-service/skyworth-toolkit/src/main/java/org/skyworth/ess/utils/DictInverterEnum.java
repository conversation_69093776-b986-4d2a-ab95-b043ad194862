package org.skyworth.ess.utils;

public enum DictInverterEnum {
	Initial_mode(0,"Initial mode"),
	Standby_mode(1,"Standby mode"),
	on_mode(3,"On-Grid mode"),
	Off_mode(4,"Off-Grid mode"),
	Fault_mode(5,"Fault mode"),
	Shutdown_mode(9,"Shutdown mode");

	private int code;
	private String message;

	private DictInverterEnum(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictInverterEnum match(int key) {

		DictInverterEnum result = null;

		for (DictInverterEnum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictInverterEnum catchMessage(String msg) {

		DictInverterEnum result = null;

		for (DictInverterEnum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
