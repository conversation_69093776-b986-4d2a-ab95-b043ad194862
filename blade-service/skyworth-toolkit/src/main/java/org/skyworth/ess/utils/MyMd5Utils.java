package org.skyworth.ess.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-27 10:29
 **/
public class MyMd5Utils {
	private static final Logger logger = LoggerFactory.getLogger(MyMd5Utils.class);
	private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e',
		'f'};

	public static String getMd5(String inStr) {
		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
		} catch (Exception e) {
			logger.error("MyMd5Utils->getMD5 An exception occurred while obtaining MD5 encryption,error:", e);
			return "";
		}
		char[] charArray = inStr.toCharArray();
		byte[] byteArray = new byte[charArray.length];
		for (int i = 0; i < charArray.length; i++) {
			byteArray[i] = (byte) charArray[i];
		}
		byte[] md5Bytes = md5.digest(byteArray);
		StringBuilder hexValue = new StringBuilder();
		for (byte md5Byte : md5Bytes) {
			int val = ((int) md5Byte) & 0xff;
			if (val < 16) {
				hexValue.append("0");
			}
			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString();
	}

	public static String getMd5(InputStream fileStream) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] buffer = new byte[2048];
			int length = -1;
			while ((length = fileStream.read(buffer)) != -1) {
				md.update(buffer, 0, length);
			}
			byte[] b = md.digest();
			return byteToHexString(b);
		} catch (Exception ex) {
			logger.error("MyMd5Utils->getMD5 An exception occurred while obtaining MD5 encryption,error:", ex);
			return null;
		} finally {
			try {
				fileStream.close();
			} catch (IOException e) {
				logger.error("MyMd5Utils->getMD5 Close file stream exception,error:", e);
			}
		}
	}

	private static String byteToHexString(byte[] tmp) {
		String s;
		// 用字节表示就是 16 个字节
		char str[] = new char[16 * 2];
		// 所以表示成 16 进制需要 32 个字符
		int k = 0;
		// 从第一个字节开始，对 MD5 的每一个字节
		for (int i = 0; i < 16; i++) {
			// 转换成 16 进制字符的转换 取第 i 个字节
			byte byte0 = tmp[i];
			// 取字节中高 4 位的数字转换,
			str[k++] = HEX_DIGITS[byte0 >>> 4 & 0xf];
			// >>> 为逻辑右移，将符号位一起右移取字节中低 4 位的数字转
			str[k++] = HEX_DIGITS[byte0 & 0xf];
		}
		// 换后的结果转换为字符串
		s = new String(str);
		return s;
	}

}
